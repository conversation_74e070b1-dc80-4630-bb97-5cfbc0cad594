# التبعيات المطلوبة لنظام الإشعارات

## إضافة التبعيات في pubspec.yaml:

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # التبعيات الموجودة...
  supabase_flutter: ^2.5.6
  flutter_screenutil: ^5.9.0
  
  # التبعيات الجديدة للإشعارات
  flutter_local_notifications: ^17.2.2
  permission_handler: ^11.3.1
  timezone: ^0.9.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
```

## إعدادات Android (android/app/src/main/AndroidManifest.xml):

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- أذونات الإشعارات -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- Android 13+ notification permission -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    
    <application
        android:label="iihc_user"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        
        <!-- إعدادات الإشعارات المحلية -->
        <receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" />
        <receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
            </intent-filter>
        </receiver>
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            
            <!-- Meta-data للإشعارات -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
```

## إعدادات iOS (ios/Runner/Info.plist):

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- الإعدادات الموجودة... -->
    
    <!-- إعدادات الإشعارات -->
    <key>UIBackgroundModes</key>
    <array>
        <string>fetch</string>
        <string>remote-notification</string>
    </array>
    
    <!-- أذونات الإشعارات -->
    <key>NSUserNotificationAlertStyle</key>
    <string>alert</string>
    
</dict>
</plist>
```

## إعدادات إضافية لـ Android (android/app/build.gradle):

```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        applicationId "com.example.iihc_user"
        minSdkVersion 21  // الحد الأدنى للإشعارات المحلية
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    
    // باقي الإعدادات...
}
```

## تهيئة الإشعارات في main.dart:

```dart
import 'package:flutter/material.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'core/services/local_notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة timezone
  tz.initializeTimeZones();
  
  // تهيئة الإشعارات المحلية
  await LocalNotificationService().initialize();
  
  runApp(MyApp());
}
```

## استخدام الإشعارات في التطبيق:

```dart
// عرض إشعار فوري
await LocalNotificationService().showNotification(
  id: 1,
  title: 'عنوان الإشعار',
  body: 'محتوى الإشعار',
  channelId: 'appointments',
  channelName: 'إشعارات المواعيد',
);

// جدولة إشعار
await LocalNotificationService().scheduleNotification(
  id: 2,
  title: 'تذكير بموعد',
  body: 'لديك موعد غداً في الساعة 10:00 صباحاً',
  scheduledDate: DateTime.now().add(Duration(hours: 24)),
);
```

## ملاحظات مهمة:

### للـ Android:
- يدعم جميع إصدارات Android من API 21+
- يتطلب إذن خاص في Android 13+ (API 33+)
- يدعم الإشعارات المجدولة والفورية
- يدعم الاهتزاز والأصوات والأضواء

### للـ iOS:
- يدعم جميع إصدارات iOS من 10.0+
- يتطلب موافقة المستخدم على الإشعارات
- يدعم الإشعارات المحلية والمجدولة
- يدعم الأصوات والشارات

### الميزات المدعومة:
- ✅ إشعارات فورية
- ✅ إشعارات مجدولة
- ✅ إلغاء الإشعارات
- ✅ أولويات مختلفة
- ✅ أصوات مخصصة
- ✅ اهتزاز وأضواء (Android)
- ✅ شارات (iOS)
- ✅ معالجة النقر على الإشعار
- ✅ التوافق مع جميع الإصدارات

## اختبار النظام:

1. **تثبيت التبعيات:**
   ```bash
   flutter pub get
   ```

2. **تشغيل التطبيق:**
   ```bash
   flutter run
   ```

3. **اختبار الإشعارات:**
   - افتح ص��حة الإشعارات
   - تحقق من عرض الإشعارات
   - اختبر البحث والفلترة
   - اختبر تحديد الإشعارات كمقروءة
   - اختبر حذف الإشعارات