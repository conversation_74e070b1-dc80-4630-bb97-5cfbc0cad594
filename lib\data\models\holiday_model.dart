class HolidayModel {
  final String id;
  final String name;
  final DateTime date;
  final String? description;
  final bool isRecurring;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool? isActive;

  HolidayModel({
    required this.id,
    required this.name,
    required this.date,
    this.description,
    this.isRecurring = false,
    this.createdAt,
    this.updatedAt,
    this.isActive,
  });

  factory HolidayModel.fromJson(Map<String, dynamic> json) {
    return HolidayModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      date: json['date'] != null 
          ? DateTime.parse(json['date']) 
          : DateTime.now(),
      description: json['description'],
      isRecurring: json['is_recurring'] ?? false,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      isActive: json['is_active'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'date': date.toIso8601String().split('T')[0],
      'description': description,
      'is_recurring': isRecurring,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_active': isActive,
    };
  }

  HolidayModel copyWith({
    String? id,
    String? name,
    DateTime? date,
    String? description,
    bool? isRecurring,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return HolidayModel(
      id: id ?? this.id,
      name: name ?? this.name,
      date: date ?? this.date,
      description: description ?? this.description,
      isRecurring: isRecurring ?? this.isRecurring,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  // Helper methods
  bool get hasDescription => description != null && description!.isNotEmpty;
  bool get hasActiveStatus => isActive != null;
  
  String get displayName => name.isNotEmpty ? name : 'عطلة غير محددة';
  String get displayDescription => description ?? 'لا يوجد وصف';
  
  // Date helpers
  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  String get formattedDateWithDay {
    const weekDays = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 
      'الجمعة', 'السبت', 'الأحد'
    ];
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    final weekDay = weekDays[date.weekday - 1];
    final month = months[date.month - 1];
    
    return '$weekDay، ${date.day} $month ${date.year}';
  }
  
  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }
  
  // Date status helpers
  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }
  
  bool get isPast => date.isBefore(DateTime.now());
  bool get isFuture => date.isAfter(DateTime.now());
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
           date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }
  
  bool get isThisMonth {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }
  
  bool get isThisYear {
    final now = DateTime.now();
    return date.year == now.year;
  }
  
  int get daysFromNow {
    final now = DateTime.now();
    return date.difference(now).inDays;
  }
  
  String get timeFromNow {
    if (isToday) return 'اليوم';
    if (isPast) {
      final days = DateTime.now().difference(date).inDays;
      if (days == 1) return 'أمس';
      return 'منذ $days ${days <= 10 ? 'أيام' : 'يوماً'}';
    } else {
      final days = daysFromNow;
      if (days == 1) return 'غداً';
      return 'خلال $days ${days <= 10 ? 'أيام' : 'يوماً'}';
    }
  }
  
  // Recurring helpers
  String get recurringText => isRecurring ? 'عطلة سنوية' : 'عطلة لمرة واحدة';
  
  // Get next occurrence for recurring holidays
  DateTime? get nextOccurrence {
    if (!isRecurring) return null;
    
    final now = DateTime.now();
    var nextDate = DateTime(now.year, date.month, date.day);
    
    // If the date has passed this year, get next year's occurrence
    if (nextDate.isBefore(now)) {
      nextDate = DateTime(now.year + 1, date.month, date.day);
    }
    
    return nextDate;
  }
  
  String get nextOccurrenceText {
    final next = nextOccurrence;
    if (next == null) return 'غير متكررة';
    
    return 'التكرار التالي: ${next.day}/${next.month}/${next.year}';
  }
  
  // Status helpers
  bool get isActiveHoliday => isActive ?? true;
  String get statusText => isActiveHoliday ? 'نشط' : 'غير نشط';
  
  // Holiday type detection based on name
  bool get isReligiousHoliday {
    final name = this.name.toLowerCase();
    return name.contains('عيد') || 
           name.contains('رمضان') || 
           name.contains('حج') || 
           name.contains('مولد') ||
           name.contains('إسراء') ||
           name.contains('معراج');
  }
  
  bool get isNationalHoliday {
    final name = this.name.toLowerCase();
    return name.contains('ثورة') || 
           name.contains('استقلال') || 
           name.contains('وطني') ||
           name.contains('تحرير') ||
           name.contains('نصر');
  }
  
  bool get isNewYear {
    final name = this.name.toLowerCase();
    return name.contains('سنة') && name.contains('جديدة');
  }
  
  String get holidayType {
    if (isReligiousHoliday) return 'عطلة دينية';
    if (isNationalHoliday) return 'عطلة وطنية';
    if (isNewYear) return 'رأس السنة';
    return 'عطلة عامة';
  }
  
  // Priority for sorting (upcoming holidays first, then by date)
  int get priority {
    if (isToday) return 5;
    if (isFuture && daysFromNow <= 7) return 4; // This week
    if (isFuture && daysFromNow <= 30) return 3; // This month
    if (isFuture) return 2;
    return 1; // Past holidays
  }
  
  @override
  String toString() {
    return 'HolidayModel(id: $id, name: $name, date: $formattedDate, recurring: $isRecurring)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HolidayModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
