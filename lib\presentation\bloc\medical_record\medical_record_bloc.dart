import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'medical_record_event.dart';
import 'medical_record_state.dart';
import '../../../core/utils/app_logger.dart';
import '../../../data/models/patient_model.dart';
import '../../../data/models/examination_model.dart';
import '../../../data/models/homework_model.dart';

class MedicalRecordBloc extends Bloc<MedicalRecordEvent, MedicalRecordState> {
  final SupabaseClient _supabase = Supabase.instance.client;

  MedicalRecordBloc() : super(const MedicalRecordInitial()) {
    on<LoadMedicalRecord>(_onLoadMedicalRecord);
    on<LoadMedicalRecordByAuthId>(_onLoadMedicalRecordByAuthId);
    on<RefreshMedicalRecord>(_onRefreshMedicalRecord);
    on<LoadPatientInfo>(_onLoadPatientInfo);
    on<LoadExaminations>(_onLoadExaminations);
    on<LoadHomework>(_onLoadHomework);
    on<ChangeTab>(_onChangeTab);
    on<ClearMedicalRecord>(_onClearMedicalRecord);
  }

  Future<void> _onLoadMedicalRecord(
    LoadMedicalRecord event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      emit(const MedicalRecordLoading());

      AppLogger.info(
        'Loading complete medical record for patient: ${event.patientId}',
        category: LogCategory.bloc,
      );

      // Load all data concurrently
      final results = await Future.wait([
        _getPatientInfo(event.patientId),
        _getExaminations(event.patientId),
        _getHomework(event.patientId),
      ]);

      final patient = results[0] as PatientModel?;
      final examinations = results[1] as List<ExaminationModel>;
      final homework = results[2] as List<HomeworkModel>;

      if (patient == null) {
        emit(const MedicalRecordError('لم يتم العثور على بيانات المريض'));
        return;
      }

      emit(
        MedicalRecordLoaded(
          patient: patient,
          examinations: examinations,
          homework: homework,
        ),
      );

      AppLogger.info(
        'Medical record loaded successfully',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading medical record',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحميل السجل الطبي: ${e.toString()}'));
    }
  }

  Future<void> _onLoadMedicalRecordByAuthId(
    LoadMedicalRecordByAuthId event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      emit(const MedicalRecordLoading());

      AppLogger.info(
        'Loading medical record by auth ID: ${event.authId}',
        category: LogCategory.bloc,
      );

      final patient = await _getPatientById(event.authId);

      if (patient == null) {
        emit(const MedicalRecordError('لم يتم العثور على بيانات المريض'));
        return;
      }

      // Now load the complete record using patient ID
      add(LoadMedicalRecord(patient.id));
    } catch (e) {
      AppLogger.error(
        'Error loading medical record by auth ID',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحميل السجل الطبي: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshMedicalRecord(
    RefreshMedicalRecord event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is MedicalRecordLoaded) {
        emit(currentState.copyWith(isRefreshing: true));
      }

      AppLogger.info(
        'Refreshing medical record for patient: ${event.patientId}',
        category: LogCategory.bloc,
      );

      // Load all data concurrently
      final results = await Future.wait([
        _getPatientInfo(event.patientId),
        _getExaminations(event.patientId),
        _getHomework(event.patientId),
      ]);

      final patient = results[0] as PatientModel?;
      final examinations = results[1] as List<ExaminationModel>;
      final homework = results[2] as List<HomeworkModel>;

      if (patient == null) {
        emit(const MedicalRecordError('لم يتم العثور على بيانات المريض'));
        return;
      }

      final currentTabIndex =
          currentState is MedicalRecordLoaded
              ? currentState.currentTabIndex
              : 0;

      emit(
        MedicalRecordLoaded(
          patient: patient,
          examinations: examinations,
          homework: homework,
          currentTabIndex: currentTabIndex,
          isRefreshing: false,
        ),
      );

      AppLogger.info(
        'Medical record refreshed successfully',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error refreshing medical record',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحديث السجل الطبي: ${e.toString()}'));
    }
  }

  Future<void> _onLoadPatientInfo(
    LoadPatientInfo event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading patient info: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final patient = await _getPatientInfo(event.patientId);

      if (patient == null) {
        emit(const MedicalRecordError('لم يتم العثور على بيانات المريض'));
        return;
      }

      emit(PatientInfoLoaded(patient));
      AppLogger.info(
        'Patient info loaded successfully',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading patient info',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError('حدث خطأ في تحميل بيانات المريض: ${e.toString()}'),
      );
    }
  }

  Future<void> _onLoadExaminations(
    LoadExaminations event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading examinations: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final examinations = await _getExaminations(event.patientId);

      emit(ExaminationsLoaded(examinations));
      AppLogger.info(
        'Examinations loaded successfully: ${examinations.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading examinations',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحميل الفحوصات: ${e.toString()}'));
    }
  }

  Future<void> _onLoadHomework(
    LoadHomework event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading homework: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final homework = await _getHomework(event.patientId);

      emit(HomeworkLoaded(homework));
      AppLogger.info(
        'Homework loaded successfully: ${homework.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading homework',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحميل الواجبات: ${e.toString()}'));
    }
  }

  void _onChangeTab(ChangeTab event, Emitter<MedicalRecordState> emit) {
    final currentState = state;
    if (currentState is MedicalRecordLoaded) {
      emit(currentState.copyWith(currentTabIndex: event.tabIndex));
      AppLogger.info(
        'Tab changed to index: ${event.tabIndex}',
        category: LogCategory.bloc,
      );
    } else {
      emit(TabChanged(event.tabIndex));
    }
  }

  void _onClearMedicalRecord(
    ClearMedicalRecord event,
    Emitter<MedicalRecordState> emit,
  ) {
    emit(const MedicalRecordInitial());
    AppLogger.info('Medical record cleared', category: LogCategory.bloc);
  }

  // Private helper methods
  Future<PatientModel?> _getPatientInfo(String patientId) async {
    try {
      final response =
          await _supabase
              .from('patients')
              .select()
              .eq('id', patientId)
              .single();

      return PatientModel.fromJson(response);
    } catch (e) {
      AppLogger.error(
        'Error fetching patient info',
        category: LogCategory.database,
        error: e,
      );
      return null;
    }
  }

  Future<PatientModel?> _getPatientById(String patientId) async {
    try {
      final response =
          await _supabase
              .from('patients')
              .select()
              .eq('id', patientId)
              .single();

      return PatientModel.fromJson(response);
    } catch (e) {
      AppLogger.error(
        'Error fetching patient by ID',
        category: LogCategory.database,
        error: e,
      );
      return null;
    }
  }

  Future<List<ExaminationModel>> _getExaminations(String patientId) async {
    try {
      final response = await _supabase
          .from('examinations')
          .select()
          .eq('patient_id', patientId)
          .order('examination_date', ascending: false);

      return (response as List)
          .map((json) => ExaminationModel.fromJson(json))
          .toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching examinations',
        category: LogCategory.database,
        error: e,
      );
      return [];
    }
  }

  Future<List<HomeworkModel>> _getHomework(String patientId) async {
    try {
      final response = await _supabase
          .from('homework')
          .select()
          .eq('patient_id', patientId)
          .order('assigned_date', ascending: false);

      return (response as List)
          .map((json) => HomeworkModel.fromJson(json))
          .toList();
    } catch (e) {
      AppLogger.error(
        'Error fetching homework',
        category: LogCategory.database,
        error: e,
      );
      return [];
    }
  }
}
