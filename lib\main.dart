import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import 'core/constants/app_strings.dart';
import 'core/services/fcm_integration_service.dart';
import 'core/themes/app_theme.dart';
import 'core/observers/app_bloc_observer.dart';
import 'core/utils/app_logger.dart';
import 'core/widgets/error_boundary.dart';
import 'core/services/shared_preferences_service.dart';
import 'data/repositories/patient_repository.dart';
import 'presentation/bloc/auth/auth_bloc.dart';
import 'presentation/bloc/auth/auth_events.dart';
import 'presentation/bloc/medical_record/medical_record_bloc.dart';
import 'presentation/pages/auth/login_page.dart';
import 'presentation/pages/main/main_page.dart';
import 'presentation/pages/splash/splash_page.dart';
import 'presentation/pages/onboarding/onboarding_page.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تقييد التطبيق على الوضع العمودي فقط
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // تهيئة SharedPreferences
  await SharedPreferencesService.init();

  // تهيئة Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  

  // تهيئة Supabase - Updated for IIHC project
  try {
    await Supabase.initialize(
      url: 'https://xqvdkdjnrcytswvfrkog.supabase.co',
      anonKey:
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.zaHSYMMK1QIRwCckgZXhT287rdW2IQUbY5Ag4U7PiRg',
    );
    print('✅ Supabase initialized successfully');
  } catch (e) {
    print('⚠️ Supabase initialization failed: $e');
    // يمكن للتطبيق أن يعمل بدون Supabase في البداية
  }

  // تهيئة خدمة FCM بعد Supabase
  try {
    await FCMIntegrationService().initialize();
    print('✅ FCM initialized successfully');
  } catch (e) {
    // في حالة فشل تهيئة FCM، استمر في تشغيل التطبيق
    print('⚠️ FCM initialization failed: $e');
  }

  // تهيئة BlocObserver
  Bloc.observer = AppBlocObserver();

  // تشغيل التطبيق
  runApp(const IIHCApp());
}

/// التطبيق الرئيسي - IIHC
class IIHCApp extends StatelessWidget {
  const IIHCApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ErrorBoundary(
      onError: (error, stackTrace) {
        AppLogger.error(
          'Unhandled error in app',
          category: LogCategory.ui,
          error: error,
          stackTrace: stackTrace,
        );
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create:
                (context) => AuthBloc(
                  supabase: Supabase.instance.client,
                  patientRepository: PatientRepository(),
                  prefs: SharedPreferencesService(),
                ),
            lazy: false,
          ),
          BlocProvider(create: (context) => MedicalRecordBloc(), lazy: true),
        ],
        child: ScreenUtilInit(
          designSize: const Size(375, 812),
          minTextAdapt: true,
          splitScreenMode: true,
          builder: (context, child) {
            return Directionality(
              textDirection: TextDirection.rtl,
              child: MaterialApp(
                title: AppStrings.appName,
                debugShowCheckedModeBanner: false,
                theme: AppTheme.lightTheme,
                locale: const Locale('ar'),
                supportedLocales: const [Locale('ar'), Locale('en')],
                localizationsDelegates: const [
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                builder: (context, widget) {
                  return Directionality(
                    textDirection: TextDirection.rtl,
                    child: widget!,
                  );
                },

                // الصفحة الرئيسية
                home: const AppWrapper(),

                // التوجيه
                routes: {
                  '/login': (context) => const LoginPage(),
                  '/main': (context) => const MainPage(),
                },
              ),
            );
          },
        ),
      ),
    );
  }
}

/// Wrapper بسيط للتطبيق
class AppWrapper extends StatefulWidget {
  const AppWrapper({super.key});

  @override
  State<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends State<AppWrapper> {
  bool _showSplash = true;
  bool _isLoggedIn = false;
  bool _isFirstTime = true;

  @override
  void initState() {
    super.initState();
    _checkUserStatus();
  
  }

  /// إعداد callbacks للإشعارات


  Future<void> _checkUserStatus() async {
    // عرض Splash لمدة كافية (حتى يظهر اسم العيادة ويبقى ثانية)
    await Future.delayed(const Duration(milliseconds: 4300));

    // فحص حالة المستخدم من SharedPreferences
    final prefs = SharedPreferencesService();
    final isFirstTime = await prefs.isFirstTime();
    final isLoggedIn = await prefs.isLoggedIn();

    if (mounted) {
      setState(() {
        _showSplash = false;
        _isFirstTime = isFirstTime;
        _isLoggedIn = isLoggedIn;
      });

      // إذا كان مسجل دخول، تشغيل AuthBloc في الخلفية فقط
      if (isLoggedIn && !isFirstTime) {
        context.read<AuthBloc>().add(const CheckAuthStatus());
        // لا نحمل أي بيانات أخرى عند بدء التطبيق لتحسين الأداء
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // عرض Splash Screen
    if (_showSplash) {
      return const SplashPage();
    }

    // إذا كانت أول مرة، عرض Onboarding
    if (_isFirstTime) {
      return const OnboardingPage();
    }

    // إذا كان مسجل دخول، عرض التطبيق
    if (_isLoggedIn) {
      return const MainPage();
    }

    // إذا لم يكن مسجل دخول، عرض صفحة تسجيل الدخول
    return const LoginPage();
  }
}