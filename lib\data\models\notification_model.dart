class NotificationModel {
  final String id;
  final String? userId;
  final String type;
  final String title;
  final String? message;
  final String? content;
  final String? description;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  final bool isRead;
  final String? imageUrl;
  final String? actionUrl;

  NotificationModel({
    required this.id,
    this.userId,
    required this.type,
    required this.title,
    this.message,
    this.content,
    this.description,
    this.data,
    required this.createdAt,
    this.isRead = false,
    this.imageUrl,
    this.actionUrl,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      userId: json['user_id'],
      type: json['type'] ?? '',
      title: json['title'] ?? '',
      message: json['message'],
      content: json['content'],
      description: json['description'],
      data: json['data'] is Map ? Map<String, dynamic>.from(json['data']) : null,
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      isRead: json['is_read'] ?? false,
      imageUrl: json['image_url'],
      actionUrl: json['action_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type,
      'title': title,
      'message': message,
      'content': content,
      'description': description,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'is_read': isRead,
      'image_url': imageUrl,
      'action_url': actionUrl,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? type,
    String? title,
    String? message,
    String? content,
    String? description,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? isRead,
    String? imageUrl,
    String? actionUrl,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      content: content ?? this.content,
      description: description ?? this.description,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
    );
  }

  /// الحصول على المحتوى النصي للإشعار
  String get bodyText {
    return message ?? content ?? description ?? '';
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  String get iconPath {
    switch (type.toLowerCase()) {
      case 'appointment_confirmed':
      case 'appointment_reminder':
        return 'assets/icons/calendar_check.png';
      case 'appointment_cancelled':
        return 'assets/icons/calendar_cancel.png';
      case 'appointment_rescheduled':
        return 'assets/icons/calendar_edit.png';
      case 'payment_received':
      case 'payment_reminder':
        return 'assets/icons/payment.png';
      case 'general':
      case 'announcement':
        return 'assets/icons/info.png';
      default:
        return 'assets/icons/notification.png';
    }
  }

  /// الحصول على لون الإشعار حسب النوع
  String get colorHex {
    switch (type.toLowerCase()) {
      case 'appointment_confirmed':
        return '#4CAF50'; // أخضر
      case 'appointment_cancelled':
        return '#F44336'; // أحمر
      case 'appointment_rescheduled':
        return '#FF9800'; // برتقالي
      case 'payment_received':
        return '#2196F3'; // أزرق
      case 'payment_reminder':
        return '#FF5722'; // أحمر برتقالي
      case 'general':
      case 'announcement':
        return '#9C27B0'; // بنفسجي
      default:
        return '#607D8B'; // رمادي مزرق
    }
  }

  /// تحويل الوقت إلى نص عربي
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }

  /// تحويل النوع إلى نص عربي
  String get typeDisplayName {
    switch (type.toLowerCase()) {
      case 'appointment_confirmed':
        return 'تأكيد موعد';
      case 'appointment_cancelled':
        return 'إلغاء موعد';
      case 'appointment_rescheduled':
        return 'تعديل موعد';
      case 'appointment_reminder':
        return 'تذكير بموعد';
      case 'payment_received':
        return 'استلام دفعة';
      case 'payment_reminder':
        return 'تذكير بدفعة';
      case 'general':
        return 'إشعار عام';
      case 'announcement':
        return 'إعلان';
      default:
        return 'إشعار';
    }
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, type: $type, title: $title, isRead: $isRead, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}