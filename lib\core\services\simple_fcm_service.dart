import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';

/// معالج الرسائل في الخلفية (يجب أن يكون في المستوى الأعلى)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    // تهيئة Firebase إذا لم يكن مهيأ
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp();
    }

    AppLogger.info(
      '🔔 Background message received',
      category: LogCategory.notification,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'title': message.notification?.title ?? 'No title',
        'body': message.notification?.body ?? 'No body',
        'data': message.data.toString(),
      },
    );
  } catch (e) {
    AppLogger.error('Error in background message handler', error: e);
  }
}

/// خدمة FCM بسيطة تعتمد على خدمة Android المخصصة
class SimpleFCMService {
  static final SimpleFCMService _instance = SimpleFCMService._internal();
  factory SimpleFCMService() => _instance;
  SimpleFCMService._internal();

  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final SupabaseClient _supabase = Supabase.instance.client;

  bool _isInitialized = false;
  String? _fcmToken;

  /// تهيئة خدمة FCM
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      AppLogger.info(
        '🚀 Initializing Simple FCM Service',
        category: LogCategory.notification,
      );

      // 1. طلب الأذونات
      await _requestPermissions();

      // 2. إعداد معالجات الرسائل
      await _setupMessageHandlers();

      // 3. الحصول على FCM Token
      await _getFCMToken();

      // 4. حفظ التوكن في قاعدة البيانات
      await _saveTokenToDatabase();

      _isInitialized = true;
      AppLogger.info(
        '✅ Simple FCM Service initialized successfully',
        category: LogCategory.notification,
      );

      return true;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize Simple FCM Service',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// طلب الأذونات
  Future<void> _requestPermissions() async {
    try {
      // طلب أذونات Firebase مع إعدادات محسنة للـ Release
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      AppLogger.info(
        'FCM Permissions granted: ${settings.authorizationStatus == AuthorizationStatus.authorized}',
        category: LogCategory.notification,
        data: {
          'status': settings.authorizationStatus.toString(),
          'alert': settings.alert.toString(),
          'badge': settings.badge.toString(),
          'sound': settings.sound.toString(),
        },
      );

      // تفعيل الإشعارات التلقائية (مهم للـ Release)
      await _firebaseMessaging.setAutoInitEnabled(true);

      // تعيين إعدادات المقدمة (مهم للـ Release)
      await _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );
    } catch (e) {
      AppLogger.error(
        'Failed to request permissions',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// إعداد معالجات الرسائل
  Future<void> _setupMessageHandlers() async {
    try {
      // معالج الرسائل في المقدمة - لا نحتاج لعرض إشعار محلي
      // لأن خدمة Android المخصصة ستتولى ذلك
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // معالج النقر على الإشعار
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      // معالج الرسائل في الخلفية
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // التحقق من الرسائل عند فتح التطبيق
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleNotificationTap(initialMessage);
      }

      AppLogger.info(
        'Message handlers setup complete',
        category: LogCategory.notification,
      );
    } catch (e) {
      AppLogger.error(
        'Failed to setup message handlers',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالج الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      AppLogger.info(
        '📱 Foreground message received - Android service will handle display',
        category: LogCategory.notification,
        data: {
          'title': message.notification?.title ?? 'No title',
          'body': message.notification?.body ?? 'No body',
          'data': message.data.toString(),
        },
      );

      // لا نحتاج لعرض إشعار محلي هنا
      // خدمة Android المخصصة ستتولى عرض الإشعار
    } catch (e) {
      AppLogger.error(
        'Error handling foreground message',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالج النقر على الإشعار
  void _handleNotificationTap(RemoteMessage message) {
    try {
      AppLogger.info(
        '👆 Notification tapped',
        category: LogCategory.notification,
        data: {
          'messageId': message.messageId ?? 'No ID',
          'data': message.data.toString(),
        },
      );

      // يمكن إضافة منطق التنقل هنا
      final notificationType = message.data['type'];
      if (notificationType != null) {
        _handleNotificationNavigation(notificationType, message.data);
      }
    } catch (e) {
      AppLogger.error(
        'Error handling notification tap',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالج التنقل حسب نوع الإشعار
  void _handleNotificationNavigation(String type, Map<String, dynamic> data) {
    try {
      switch (type) {
        case 'appointment_cancelled':
        case 'appointment_rescheduled':
          AppLogger.info(
            'Navigate to appointments page',
            category: LogCategory.notification,
          );
          break;
        case 'new_message':
          AppLogger.info(
            'Navigate to messages page',
            category: LogCategory.notification,
          );
          break;
        default:
          AppLogger.info(
            'Navigate to home page',
            category: LogCategory.notification,
          );
          break;
      }
    } catch (e) {
      AppLogger.error(
        'Error in notification navigation',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// الحصول على FCM Token
  Future<void> _getFCMToken() async {
    try {
      // محاولة الحصول على التوكن مع إعادة المحاولة
      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          _fcmToken = await _firebaseMessaging.getToken();
          if (_fcmToken != null) break;
        } catch (e) {
          AppLogger.warning(
            'FCM token attempt $attempt failed',
            category: LogCategory.notification,
          );
          if (attempt < 3) {
            await Future.delayed(Duration(seconds: attempt * 2));
          }
        }
      }

      if (_fcmToken != null) {
        AppLogger.info(
          'FCM Token obtained successfully',
          category: LogCategory.notification,
          data: {'tokenLength': _fcmToken!.length.toString()},
        );
      } else {
        AppLogger.error(
          'Failed to obtain FCM token after 3 attempts',
          category: LogCategory.notification,
        );
      }

      // مراقبة تغيير Token
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        AppLogger.info(
          'FCM Token refreshed',
          category: LogCategory.notification,
        );
        _saveTokenToDatabase();
      });
    } catch (e) {
      AppLogger.error(
        'Failed to get FCM token',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// حفظ التوكن في قاعدة البيانات
  Future<void> _saveTokenToDatabase() async {
    try {
      if (_fcmToken == null) {
        AppLogger.warning(
          'No FCM token to save',
          category: LogCategory.notification,
        );
        return;
      }

      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        AppLogger.warning(
          'No user logged in, cannot save FCM token',
          category: LogCategory.notification,
        );
        return;
      }

      // محاولة حفظ التوكن مع إعادة المحاولة
      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          await _supabase.from('user_fcm_tokens').upsert({
            'user_id': userId,
            'fcm_token': _fcmToken,
            'is_active': true,
            'updated_at': DateTime.now().toIso8601String(),
          });

          AppLogger.info(
            'FCM token saved to database successfully',
            category: LogCategory.notification,
            data: {'attempt': attempt.toString()},
          );
          return;
        } catch (e) {
          AppLogger.warning(
            'FCM token save attempt $attempt failed: $e',
            category: LogCategory.notification,
          );
          if (attempt < 3) {
            await Future.delayed(Duration(seconds: attempt * 2));
          }
        }
      }

      AppLogger.error(
        'Failed to save FCM token after 3 attempts',
        category: LogCategory.notification,
      );
    } catch (e) {
      AppLogger.error(
        'Error in saveTokenToDatabase',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// الحصول على FCM Token الحالي
  String? get fcmToken => _fcmToken;

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;
}
