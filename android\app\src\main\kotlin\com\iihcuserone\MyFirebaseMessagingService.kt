package com.iihcuserone

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage

class MyFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "FCMService"
        private const val CHANNEL_ID = "high_importance_channel"
        private const val NOTIFICATION_ID = 1
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🚀 FCM Service created")
        createNotificationChannel()
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        
        Log.d(TAG, "🔔 FCM Message received!")
        Log.d(TAG, "📍 From: ${remoteMessage.from}")
        Log.d(TAG, "🆔 Message ID: ${remoteMessage.messageId}")
        Log.d(TAG, "📊 Data: ${remoteMessage.data}")
        
        // طباعة تفاصيل الإشعار
        remoteMessage.notification?.let { notification ->
            Log.d(TAG, "📝 Title: ${notification.title}")
            Log.d(TAG, "📝 Body: ${notification.body}")
            Log.d(TAG, "🖼️ Icon: ${notification.icon}")
            Log.d(TAG, "🔊 Sound: ${notification.sound}")
        }

        // عرض الإشعار
        showNotification(remoteMessage)
        
        Log.d(TAG, "✅ FCM Message processing completed")
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "🔑 New FCM token received")
        Log.d(TAG, "🔑 Token: ${token.substring(0, 30)}...")
        
        // يمكن إرسال التوكن للخادم هنا إذا لزم الأمر
        sendTokenToServer(token)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "إشعارات مهمة"
            val descriptionText = "قناة للإشعارات المهمة من العيادة"
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            
            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            
            Log.d(TAG, "📢 Notification channel created: $CHANNEL_ID")
        }
    }

    private fun showNotification(remoteMessage: RemoteMessage) {
        Log.d(TAG, "🔔 Creating notification...")
        
        val title = remoteMessage.notification?.title ?: remoteMessage.data["title"] ?: "إشعار جديد"
        val body = remoteMessage.notification?.body ?: remoteMessage.data["body"] ?: "لديك إشعار جديد من العيادة"
        
        Log.d(TAG, "📝 Notification title: $title")
        Log.d(TAG, "📝 Notification body: $body")
        
        // إنشاء Intent لفتح التطبيق
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            
            // إضافة بيانات FCM للـ Intent
            remoteMessage.data.forEach { (key, value) ->
                putExtra(key, value)
                Log.d(TAG, "📎 Added extra: $key = $value")
            }
        }

        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        } else {
            PendingIntent.FLAG_ONE_SHOT
        }

        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent, pendingIntentFlags
        )

        // بناء الإشعار مع أيقونة مخصصة
        val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification) // أيقونة الإشعار المخصصة
            .setContentTitle(title)
            .setContentText(body)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setContentIntent(pendingIntent)
            .setStyle(NotificationCompat.BigTextStyle().bigText(body))
            .setColor(resources.getColor(R.color.notification_color, null))

        try {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID, notificationBuilder.build())
            
            Log.d(TAG, "✅ Notification displayed successfully: $title")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to display notification: ${e.message}")
            e.printStackTrace()
            
            // محاولة مع أيقونة النظام كـ fallback
            try {
                val fallbackBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
                    .setSmallIcon(android.R.drawable.ic_dialog_info)
                    .setContentTitle(title)
                    .setContentText(body)
                    .setAutoCancel(true)
                    .setPriority(NotificationCompat.PRIORITY_HIGH)
                    .setContentIntent(pendingIntent)
                
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.notify(NOTIFICATION_ID, fallbackBuilder.build())
                
                Log.d(TAG, "✅ Fallback notification displayed: $title")
            } catch (fallbackError: Exception) {
                Log.e(TAG, "❌ Fallback notification also failed: ${fallbackError.message}")
            }
        }
    }

    private fun sendTokenToServer(token: String) {
        // يمكن تنفيذ ��رسال التوكن للخادم هنا
        Log.d(TAG, "📤 Token should be sent to server")
        Log.d(TAG, "🔑 Token length: ${token.length}")
    }
}