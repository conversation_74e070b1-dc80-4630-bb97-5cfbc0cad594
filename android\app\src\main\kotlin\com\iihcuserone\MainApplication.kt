package com.iihcuserone

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import android.util.Log
import com.google.firebase.FirebaseApp
import com.google.firebase.messaging.FirebaseMessaging

class MainApplication : Application() {

    companion object {
        private const val TAG = "MainApplication"
        private const val CHANNEL_ID = "high_importance_channel"
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🚀 Application starting...")
        
        // تهيئة Firebase
        initializeFirebase()
        
        // إنشاء Notification Channel
        createNotificationChannel()
        
        // تهيئة FCM
        initializeFCM()
        
        Log.d(TAG, "✅ Application initialized")
    }

    private fun initializeFirebase() {
        try {
            if (FirebaseApp.getApps(this).isEmpty()) {
                FirebaseApp.initializeApp(this)
                Log.d(TAG, "✅ Firebase initialized")
            } else {
                Log.d(TAG, "ℹ️ Firebase already initialized")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Firebase initialization failed: ${e.message}")
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "إشعارات مهمة"
            val descriptionText = "قناة للإشعارات المهمة من العيادة"
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            
            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            
            Log.d(TAG, "📢 Notification channel created: $CHANNEL_ID")
        }
    }

    private fun initializeFCM() {
        try {
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    Log.w(TAG, "❌ Fetching FCM registration token failed", task.exception)
                    return@addOnCompleteListener
                }

                // Get new FCM registration token
                val token = task.result
                Log.d(TAG, "🔑 FCM Token: ${token?.substring(0, 30)}...")
            }
            
            Log.d(TAG, "✅ FCM initialized")
        } catch (e: Exception) {
            Log.e(TAG, "❌ FCM initialization failed: ${e.message}")
        }
    }
}