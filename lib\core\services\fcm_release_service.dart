import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'fcm_token_service.dart';

/// معالج الرسائل في الخلفية - يجب أن يكون في المستوى الأعلى
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // تهيئة Firebase إذا لم تكن مهيأة
  try {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp();
    }
    
    AppLogger.info(
      '🔔 Background message received',
      category: LogCategory.fcm,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'title': message.notification?.title ?? 'No title',
        'body': message.notification?.body ?? 'No body',
        'data': message.data.toString(),
      },
    );

    // عرض إشعار محلي للرسائل في الخلفية
    await FCMReleaseService._showBackgroundNotification(message);
  } catch (e) {
    if (kDebugMode) {
      print('Error in background handler: $e');
    }
  }
}

/// خدمة FCM محسنة للعمل في Release وجميع إصدارات Android/iOS
class FCMReleaseService {
  static final FCMReleaseService _instance = FCMReleaseService._internal();
  factory FCMReleaseService() => _instance;
  FCMReleaseService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  final SupabaseClient _supabase = Supabase.instance.client;

  String? _fcmToken;
  bool _isInitialized = false;

  /// الحصول على FCM Token
  String? get fcmToken => _fcmToken;

  /// تهيئة خدمة FCM
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info(
        '🚀 Initializing FCM Release service',
        category: LogCategory.fcm,
      );

      // تهيئة الإشعارات المحلية أولاً
      await _initializeLocalNotifications();

      // طلب الأذونات
      await _requestPermissions();

      // إعداد معالج الرسائل في الخلفية
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // الحصول على FCM Token
      await _getFCMToken();

      // إعداد معالجات الرسائل
      _setupMessageHandlers();

      _isInitialized = true;

      AppLogger.info(
        '✅ FCM Release service initialized successfully',
        category: LogCategory.fcm,
        data: {'fcmToken': _fcmToken ?? 'No token'},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize FCM Release service',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    try {
      // إعدادات Android
      const androidSettings = AndroidInitializationSettings('@mipmap/launcher_icon');
      
      // إعدادات iOS
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // إنشاء قناة إشعارات لـ Android
      if (Platform.isAndroid) {
        await _createNotificationChannel();
      }

      AppLogger.info(
        '📱 Local notifications initialized',
        category: LogCategory.fcm,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize local notifications',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// إنشاء قناة إشعارات لـ Android
  Future<void> _createNotificationChannel() async {
    const channel = AndroidNotificationChannel(
      'high_importance_channel',
      'إشعارات مهمة',
      description: 'قناة للإشعارات المهمة',
      importance: Importance.high,
      enableVibration: true,
      enableLights: true,
      playSound: true,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    try {
      // أذونات Firebase Messaging
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      AppLogger.info(
        '🔐 FCM permissions requested',
        category: LogCategory.fcm,
        data: {'status': settings.authorizationStatus.toString()},
      );

      // أذونات الإشعارات المحلية لـ Android 13+
      if (Platform.isAndroid) {
        await _localNotifications
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();
      }

      // أذونات iOS
      if (Platform.isIOS) {
        await _localNotifications
            .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(
              alert: true,
              badge: true,
              sound: true,
            );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to request permissions',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// الحصول على FCM Token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();

      AppLogger.info(
        '🔑 FCM token obtained',
        category: LogCategory.fcm,
        data: {'token': _fcmToken ?? 'No token'},
      );

      // مراقبة تغيير Token
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        AppLogger.info(
          '🔄 FCM token refreshed',
          category: LogCategory.fcm,
          data: {'newToken': newToken},
        );

        // حفظ التوكن الجديد
        _saveTokenToServer(newToken);
      });
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get FCM token',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// إعداد معالجات الرسائل
  void _setupMessageHandlers() {
    // معالج الرسائل في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // معالج النقر على الإشعار
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // التحقق من الرسائل عند فتح التطبيق
    _handleInitialMessage();
  }

  /// معالج الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    AppLogger.info(
      '📱 Foreground message received',
      category: LogCategory.fcm,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'title': message.notification?.title ?? 'No title',
        'body': message.notification?.body ?? 'No body',
        'data': message.data.toString(),
      },
    );

    // عرض إشعار محلي
    await _showLocalNotification(message);
  }

  /// معالج النقر على الإشعار
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    AppLogger.info(
      '👆 Notification tapped',
      category: LogCategory.fcm,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'data': message.data.toString(),
      },
    );

    // معالجة التنقل
    _handleNavigation(message.data);
  }

  /// معالج الرسالة الأولية
  Future<void> _handleInitialMessage() async {
    try {
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        AppLogger.info(
          '🚀 Initial message found',
          category: LogCategory.fcm,
          data: {
            'messageId': initialMessage.messageId ?? 'No ID',
            'data': initialMessage.data.toString(),
          },
        );

        _handleNavigation(initialMessage.data);
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle initial message',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// عرض إشعار محلي
  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      final notification = message.notification;
      if (notification == null) return;

      const androidDetails = AndroidNotificationDetails(
        'high_importance_channel',
        'إشعارات مهمة',
        channelDescription: 'قناة للإشعارات المهمة',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        enableLights: true,
        playSound: true,
        icon: '@mipmap/launcher_icon',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        message.hashCode,
        notification.title ?? 'إشعار جديد',
        notification.body ?? '',
        details,
        payload: _createPayload(message.data),
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to show local notification',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// عرض إشعار في الخلفية (static method)
  static Future<void> _showBackgroundNotification(RemoteMessage message) async {
    try {
      final notification = message.notification;
      if (notification == null) return;

      const androidDetails = AndroidNotificationDetails(
        'high_importance_channel',
        'إشعارات مهمة',
        channelDescription: 'قناة للإشعارات المهمة',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        enableLights: true,
        playSound: true,
        icon: '@mipmap/launcher_icon',
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        message.hashCode,
        notification.title ?? 'إشعار جديد',
        notification.body ?? '',
        details,
        payload: message.data.entries.map((e) => '${e.key}:${e.value}').join(','),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error showing background notification: $e');
      }
    }
  }

  /// معالج النقر على الإشعار المحلي
  void _onNotificationTapped(NotificationResponse response) {
    AppLogger.info(
      '👆 Local notification tapped',
      category: LogCategory.fcm,
      data: {'payload': response.payload ?? 'No payload'},
    );

    if (response.payload != null) {
      final data = <String, String>{};
      for (final pair in response.payload!.split(',')) {
        final parts = pair.split(':');
        if (parts.length == 2) {
          data[parts[0]] = parts[1];
        }
      }
      _handleNavigation(data);
    }
  }

  /// معالجة التنقل
  void _handleNavigation(Map<String, dynamic> data) {
    // يمكن تخصيص التنقل حسب نوع الإشعار
    final type = data['type'] as String?;
    
    switch (type) {
      case 'appointment':
        AppLogger.info(
          '📅 Navigate to appointments',
          category: LogCategory.navigation,
        );
        // التنقل لصفحة المواعيد
        break;
      case 'notification':
        AppLogger.info(
          '🔔 Navigate to notifications',
          category: LogCategory.navigation,
        );
        // التنقل لصفحة الإشعارات
        break;
      default:
        AppLogger.info(
          '🏠 Navigate to home',
          category: LogCategory.navigation,
        );
        // التنقل للصفحة الرئيسية
    }
  }

  /// إنشاء payload من البيا��ات
  String _createPayload(Map<String, dynamic> data) {
    return data.entries.map((e) => '${e.key}:${e.value}').join(',');
  }

  /// حفظ التوكن في الخادم
  Future<void> _saveTokenToServer(String token) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser != null) {
        final fcmTokenService = FCMTokenService();
        await fcmTokenService.saveToken(currentUser.id, token);

        AppLogger.info(
          '📤 Token saved to server',
          category: LogCategory.fcm,
          data: {'userId': currentUser.id},
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save token to server',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      AppLogger.info('Subscribed to topic: $topic', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.error(
        'Failed to subscribe to topic: $topic',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      AppLogger.info('Unsubscribed from topic: $topic', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.error(
        'Failed to unsubscribe from topic: $topic',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// التحقق من حالة الأذونات
  Future<bool> areNotificationsEnabled() async {
    try {
      if (Platform.isAndroid) {
        final androidImplementation = _localNotifications
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
        return await androidImplementation?.areNotificationsEnabled() ?? false;
      } else if (Platform.isIOS) {
        final settings = await _firebaseMessaging.getNotificationSettings();
        return settings.authorizationStatus == AuthorizationStatus.authorized;
      }
      return false;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to check notification permissions',
        category: LogCategory.fcm,
        error: e,
      );
      return false;
    }
  }

  /// معالجة تسجيل دخول المستخدم
  Future<void> onUserLogin(String userId) async {
    try {
      AppLogger.info(
        '👤 User logged in, updating FCM',
        category: LogCategory.fcm,
        data: {'userId': userId},
      );

      // التأكد من الحصول على FCM Token
      if (_fcmToken == null) {
        await _getFCMToken();
      }

      // حفظ التوكن
      if (_fcmToken != null) {
        await _saveTokenToServer(_fcmToken!);
      }

      // الاشتراك في المواضيع
      await subscribeToTopic('user_$userId');
      await subscribeToTopic('general_notifications');
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user login',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// معالجة تسجيل خروج المستخدم
  Future<void> onUserLogout() async {
    try {
      AppLogger.info(
        '👤 User logged out, cleaning up FCM',
        category: LogCategory.fcm,
      );

      // إلغاء الاشتراك من المواضيع
      await unsubscribeFromTopic('general_notifications');
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user logout',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// تنظيف الموارد
  void dispose() {
    AppLogger.info('🧹 FCM Release service disposed', category: LogCategory.fcm);
  }
}