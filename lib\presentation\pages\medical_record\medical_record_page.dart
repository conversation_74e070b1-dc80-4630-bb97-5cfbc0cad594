import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_logger.dart';
import '../../../data/models/patient_model.dart';
import '../../widgets/common/custom_app_bar.dart';
import 'tabs/patient_info_tab.dart';
import 'tabs/examinations_tab.dart';
import 'tabs/homework_tab.dart';

/// صفحة السجل الطبي - محدثة للموديلز الجديدة
class MedicalRecordPage extends StatefulWidget {
  final String? authId;

  const MedicalRecordPage({super.key, this.authId});

  @override
  State<MedicalRecordPage> createState() => _MedicalRecordPageState();
}

class _MedicalRecordPageState extends State<MedicalRecordPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  PatientModel? _currentPatient;
  String? _patientId;
  bool _isLoading = true;

  final List<String> _tabTitles = [
    'معلومات المريض',
    'الفحوصات والتقارير',
    'الواجبات',
  ];

  final List<IconData> _tabIcons = [
    Icons.person_outline,
    Icons.assignment_outlined,
    Icons.task_alt_outlined,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadPatientData();
  }

  Future<void> _loadPatientData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      String authId = widget.authId ?? '';

      // إذا لم يتم تمرير authId، استخدم المستخدم الحالي
      if (authId.isEmpty) {
        final currentUser = Supabase.instance.client.auth.currentUser;
        authId = currentUser?.id ?? '';
      }

      if (authId.isNotEmpty) {
        // البحث عن المريض باستخدام id
        final response =
            await Supabase.instance.client
                .from('patients')
                .select()
                .eq('id', authId)
                .single();

        final patient = PatientModel.fromJson(response);

        setState(() {
          _currentPatient = patient;
          _patientId = patient.id;
          _isLoading = false;
        });

        AppLogger.info(
          '📋 Medical record page loaded for patient',
          category: LogCategory.ui,
          data: {
            'patientId': patient.id,
            'patientName': patient.name,
            'patientIdUsed': authId,
          },
        );
      } else {
        setState(() {
          _isLoading = false;
        });

        AppLogger.warning(
          '⚠️ No patient ID available for medical record',
          category: LogCategory.ui,
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      AppLogger.error(
        '❌ Error loading patient data',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        body:
            _isLoading
                ? _buildLoadingState()
                : _currentPatient != null
                ? _buildLoadedState()
                : _buildErrorState(),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Scaffold(
      appBar: const CustomAppBar(title: 'السجل الطبي'),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 40.w,
              height: 40.h,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                color: AppColors.primary,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              'جاري تحميل السجل الطبي...',
              style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Scaffold(
      appBar: const CustomAppBar(title: 'السجل الطبي'),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.sp, color: AppColors.error),
            SizedBox(height: 16.h),
            Text(
              'خطأ في تحميل السجل الطبي',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.error,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'تعذر العثور على بيانات المريض',
              style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _loadPatientData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadedState() {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [_buildSliverAppBar(), _buildSliverTabBar()];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            PatientInfoTab(patientId: _patientId!),
            ExaminationsTab(patientId: _patientId!),
            HomeworkTab(patientId: _patientId!),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200.h,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'السجل الطبي',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.white,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: _buildPatientHeader(),
        ),
      ),
    );
  }

  Widget _buildPatientHeader() {
    if (_currentPatient == null) return const SizedBox.shrink();

    return Padding(
      padding: EdgeInsets.fromLTRB(20.w, 80.h, 20.w, 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // صورة المريض
              Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: AppColors.white, width: 3),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 27.r,
                  backgroundColor: AppColors.white.withValues(alpha: 0.2),
                  child: Icon(
                    _currentPatient!.isMale ? Icons.male : Icons.female,
                    size: 30.sp,
                    color: AppColors.white,
                  ),
                ),
              ),
              SizedBox(width: 16.w),

              // معلومات المريض
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _currentPatient!.displayName,
                            style: TextStyle(
                              fontSize: 20.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.white,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (_currentPatient!.isPremium) ...[
                          SizedBox(width: 8.w),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.star,
                                  size: 12.sp,
                                  color: Colors.black,
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  'مميز',
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'رقم المريض: ${_currentPatient!.displayPatientId}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Row(
                      children: [
                        Icon(
                          Icons.cake_outlined,
                          size: 14.sp,
                          color: AppColors.white.withValues(alpha: 0.8),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          _currentPatient!.displayAge,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.white.withValues(alpha: 0.8),
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Icon(
                          _currentPatient!.isMale ? Icons.male : Icons.female,
                          size: 14.sp,
                          color: AppColors.white.withValues(alpha: 0.8),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          _currentPatient!.displayGender,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildSliverTabBar() {
    return SliverPersistentHeader(
      delegate: _SliverTabBarDelegate(
        TabBar(
          controller: _tabController,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3.h,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
          unselectedLabelStyle: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
          ),
          tabs: List.generate(
            _tabTitles.length,
            (index) => Tab(
              icon: Icon(_tabIcons[index], size: 20.sp),
              text: _tabTitles[index],
            ),
          ),
        ),
      ),
      pinned: true,
    );
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverTabBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(color: AppColors.white, child: _tabBar);
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
