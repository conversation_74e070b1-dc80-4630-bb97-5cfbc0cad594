import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/patient_model.dart';
import '../../core/utils/app_logger.dart';

/// Repository للتعامل مع بيانات المرضى في Supabase
class PatientRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// إنشاء مريض جديد
  Future<PatientModel> createPatient({
    required String userId,
    required String name,
    required String email,
    String? phone,
    String? gender,
    DateTime? birthDate,
    int? age,
    String? medicalConditions,
    String? allergies,
    String? medications,
    String? supplements,
    String? physicalActivity,
    String? notes,
    List<String>? treatmentTypes,
  }) async {
    AppLogger.info(
      '🏥 Creating patient record',
      category: LogCategory.database,
      data: {
        'name': name,
        'email': email,
        'phone': phone ?? 'null',
        'gender': gender ?? 'null',
        'age': age?.toString() ?? 'null',
        'hasBirthDate': (birthDate != null).toString(),
        'hasTreatmentTypes': (treatmentTypes != null).toString(),
      },
    );

    try {
      final insertData = {
        'name': name,
        'email': email,
        'phone': phone,
        'gender': gender,
        'birth_date': birthDate?.toIso8601String().split('T')[0],
        'age': age,
        'is_premium': false,
        'medical_conditions': medicalConditions,
        'allergies': allergies,
        'medications': medications,
        'supplements': supplements,
        'physical_activity': physicalActivity,
        'notes': notes,
        'treatment_types': treatmentTypes ?? [],
      };

      // إزالة القيم null
      insertData.removeWhere((key, value) => value == null);

      AppLogger.database('INSERT', 'patients', data: insertData);

      final response =
          await _supabase.from('patients').insert(insertData).select().single();

      AppLogger.info(
        '✅ Patient record created successfully',
        category: LogCategory.database,
        data: {
          'patientId': response['id']?.toString() ?? 'null',
          'email': email,
          'name': name,
          'generatedPatientId': response['patient_id']?.toString() ?? 'null',
        },
      );

      return PatientModel.fromJson(response);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to create patient record',
        category: LogCategory.database,
        data: {
          'email': email,
          'name': name,
          'errorType': e.runtimeType.toString(),
        },
        error: e,
      );

      throw Exception('فشل في إنشاء ملف المريض: ${e.toString()}');
    }
  }

  /// الحصول على بيانات المريض بواسطة email (لأن الجدول لا يحتوي على auth_id أو user_id)
  Future<PatientModel?> getPatientByUserId(String authId) async {
    try {
      // نحصل على email المستخدم الحالي من auth
      final currentUser = _supabase.auth.currentUser;
      if (currentUser?.email == null) {
        AppLogger.warning(
          '⚠️ No current user email found',
          category: LogCategory.database,
          data: {'authId': authId},
        );
        return null;
      }

      final userEmail = currentUser!.email!;

      AppLogger.info(
        '🔍 Getting patient by email',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'email': userEmail,
        },
      );

      final response =
          await _supabase
              .from('patients')
              .select()
              .eq('email', userEmail) // البحث بالإيميل
              .maybeSingle();

      if (response == null) {
        AppLogger.info(
          'ℹ️ No patient found for email',
          category: LogCategory.database,
          data: {
            'authId': authId,
            'email': userEmail,
          },
        );
        return null;
      }

      AppLogger.info(
        '✅ Patient found successfully',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'email': userEmail,
          'patientId': response['id']?.toString() ?? 'null',
          'name': response['name']?.toString() ?? 'null',
          'patientNumber': response['patient_id']?.toString() ?? 'null',
        },
      );

      return PatientModel.fromJson(response);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get patient by email',
        category: LogCategory.database,
        data: {'authId': authId},
        error: e,
      );
      throw Exception('فشل في جلب بيانات المريض: ${e.toString()}');
    }
  }

  /// الحصول على بيانات المريض بواسطة ID
  Future<PatientModel?> getPatientById(String patientId) async {
    try {
      AppLogger.info(
        '🔍 Getting patient by ID',
        category: LogCategory.database,
        data: {'patientId': patientId},
      );

      final response =
          await _supabase
              .from('patients')
              .select()
              .eq('id', patientId)
              .maybeSingle();

      if (response == null) {
        AppLogger.info(
          'ℹ️ No patient found for ID',
          category: LogCategory.database,
          data: {'patientId': patientId},
        );
        return null;
      }

      AppLogger.info(
        '✅ Patient found successfully',
        category: LogCategory.database,
        data: {
          'patientId': patientId,
          'name': response['name']?.toString() ?? 'null',
        },
      );

      return PatientModel.fromJson(response);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get patient by ID',
        category: LogCategory.database,
        data: {'patientId': patientId},
        error: e,
      );
      throw Exception('فشل في جلب بيانات المريض: ${e.toString()}');
    }
  }

  /// تحديث بيانات المريض
  Future<PatientModel> updatePatient({
    required String authId,
    String? name,
    String? phone,
    String? gender,
    DateTime? birthDate,
    int? age,
    bool? isPremium,
    String? medicalConditions,
    String? allergies,
    String? medications,
    String? supplements,
    String? physicalActivity,
    String? notes,
    List<String>? treatmentTypes,
  }) async {
    try {
      // نحصل على email المستخدم الحالي
      final currentUser = _supabase.auth.currentUser;
      if (currentUser?.email == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      final userEmail = currentUser!.email!;

      AppLogger.info(
        '🔄 Updating patient record',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'email': userEmail,
          'hasName': (name != null).toString(),
          'hasPhone': (phone != null).toString(),
          'hasGender': (gender != null).toString(),
          'hasBirthDate': (birthDate != null).toString(),
          'hasAge': (age != null).toString(),
          'hasPremium': (isPremium != null).toString(),
        },
      );

      final updateData = <String, dynamic>{};

      if (name != null) updateData['name'] = name;
      if (phone != null) updateData['phone'] = phone;
      if (gender != null) updateData['gender'] = gender;
      if (birthDate != null) {
        updateData['birth_date'] = birthDate.toIso8601String().split('T')[0];
      }
      if (age != null) updateData['age'] = age;
      if (isPremium != null) updateData['is_premium'] = isPremium;
      if (medicalConditions != null) updateData['medical_conditions'] = medicalConditions;
      if (allergies != null) updateData['allergies'] = allergies;
      if (medications != null) updateData['medications'] = medications;
      if (supplements != null) updateData['supplements'] = supplements;
      if (physicalActivity != null) updateData['physical_activity'] = physicalActivity;
      if (notes != null) updateData['notes'] = notes;
      if (treatmentTypes != null) updateData['treatment_types'] = treatmentTypes;

      AppLogger.database('UPDATE', 'patients', data: updateData);

      final response =
          await _supabase
              .from('patients')
              .update(updateData)
              .eq('email', userEmail) // التحديث بالإيميل
              .select()
              .single();

      AppLogger.info(
        '✅ Patient record updated successfully',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'email': userEmail,
          'patientId': response['id']?.toString() ?? 'null',
        },
      );

      return PatientModel.fromJson(response);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to update patient record',
        category: LogCategory.database,
        data: {'authId': authId},
        error: e,
      );
      throw Exception('فشل في تحديث بيانات المريض: ${e.toString()}');
    }
  }

  /// حذف بيانات المريض
  Future<void> deletePatient(String authId) async {
    try {
      // نحصل على email المستخدم الحالي
      final currentUser = _supabase.auth.currentUser;
      if (currentUser?.email == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      final userEmail = currentUser!.email!;

      AppLogger.info(
        '🗑️ Deleting patient record',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'email': userEmail,
        },
      );

      await _supabase
          .from('patients')
          .delete()
          .eq('email', userEmail); // الحذف بالإيميل

      AppLogger.info(
        '✅ Patient record deleted successfully',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'email': userEmail,
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to delete patient record',
        category: LogCategory.database,
        data: {'authId': authId},
        error: e,
      );
      throw Exception('فشل في حذف بيانات المريض: ${e.toString()}');
    }
  }

  /// الحصول على جميع المرضى (للإدارة)
  Future<List<PatientModel>> getAllPatients({int? limit, int? offset}) async {
    try {
      var query = _supabase
          .from('patients')
          .select()
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await query;

      return response.map((json) => PatientModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب قائمة المرضى: ${e.toString()}');
    }
  }

  /// البحث عن المرضى
  Future<List<PatientModel>> searchPatients(String searchTerm) async {
    try {
      final response = await _supabase
          .from('patients')
          .select()
          .or(
            'name.ilike.%$searchTerm%,email.ilike.%$searchTerm%,phone.ilike.%$searchTerm%',
          )
          .order('created_at', ascending: false);

      return response.map((json) => PatientModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في البحث عن المرضى: ${e.toString()}');
    }
  }

  /// الحصول على المرضى حسب نوع العلاج
  Future<List<PatientModel>> getPatientsByTreatmentType(String treatmentType) async {
    try {
      final response = await _supabase
          .from('patients')
          .select()
          .contains('treatment_types', [treatmentType])
          .order('created_at', ascending: false);

      return response.map((json) => PatientModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المرضى حسب نوع العلاج: ${e.toString()}');
    }
  }

  /// الحصول على المرضى المميزين
  Future<List<PatientModel>> getPremiumPatients() async {
    try {
      final response = await _supabase
          .from('patients')
          .select()
          .eq('is_premium', true)
          .order('created_at', ascending: false);

      return response.map((json) => PatientModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المرضى المميزين: ${e.toString()}');
    }
  }
}