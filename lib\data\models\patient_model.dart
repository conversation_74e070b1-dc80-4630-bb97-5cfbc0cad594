class PatientModel {
  final String id;
  final String name;
  final String? email;
  final String? phone;
  final int? age;
  final DateTime? birthDate;
  final String? gender;
  final bool isPremium;
  final String? medicalConditions;
  final String? allergies;
  final String? medications;
  final String? supplements;
  final String? physicalActivity;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<String> treatmentTypes;
  final String? patientId;

  PatientModel({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.age,
    this.birthDate,
    this.gender,
    this.isPremium = false,
    this.medicalConditions,
    this.allergies,
    this.medications,
    this.supplements,
    this.physicalActivity,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.treatmentTypes = const [],
    this.patientId,
  });

  factory PatientModel.fromJson(Map<String, dynamic> json) {
    List<String> treatmentTypesList = [];
    if (json['treatment_types'] != null) {
      if (json['treatment_types'] is List) {
        treatmentTypesList = List<String>.from(json['treatment_types']);
      }
    }

    return PatientModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'],
      phone: json['phone'],
      age: json['age'],
      birthDate:
          json['birth_date'] != null
              ? DateTime.tryParse(json['birth_date'])
              : null,
      gender: json['gender'],
      isPremium: json['is_premium'] ?? false,
      medicalConditions: json['medical_conditions'],
      allergies: json['allergies'],
      medications: json['medications'],
      supplements: json['supplements'],
      physicalActivity: json['physical_activity'],
      notes: json['notes'],
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'])
              : null,
      treatmentTypes: treatmentTypesList,
      patientId: json['patient_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'age': age,
      'birth_date': birthDate?.toIso8601String().split('T')[0],
      'gender': gender,
      'is_premium': isPremium,
      'medical_conditions': medicalConditions,
      'allergies': allergies,
      'medications': medications,
      'supplements': supplements,
      'physical_activity': physicalActivity,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'treatment_types': treatmentTypes,
      'patient_id': patientId,
    };
  }

  PatientModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    int? age,
    DateTime? birthDate,
    String? gender,
    bool? isPremium,
    String? medicalConditions,
    String? allergies,
    String? medications,
    String? supplements,
    String? physicalActivity,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? treatmentTypes,
    String? patientId,
  }) {
    return PatientModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      age: age ?? this.age,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      isPremium: isPremium ?? this.isPremium,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      allergies: allergies ?? this.allergies,
      medications: medications ?? this.medications,
      supplements: supplements ?? this.supplements,
      physicalActivity: physicalActivity ?? this.physicalActivity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      treatmentTypes: treatmentTypes ?? this.treatmentTypes,
      patientId: patientId ?? this.patientId,
    );
  }

  // Helper methods
  bool get hasEmail => email != null && email!.isNotEmpty;
  bool get hasPhone => phone != null && phone!.isNotEmpty;
  bool get hasBirthDate => birthDate != null;
  bool get hasAge => age != null && age! > 0;
  bool get hasGender => gender != null && gender!.isNotEmpty;
  bool get hasMedicalConditions =>
      medicalConditions != null && medicalConditions!.isNotEmpty;
  bool get hasAllergies => allergies != null && allergies!.isNotEmpty;
  bool get hasMedications => medications != null && medications!.isNotEmpty;
  bool get hasSupplements => supplements != null && supplements!.isNotEmpty;
  bool get hasPhysicalActivity =>
      physicalActivity != null && physicalActivity!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasTreatmentTypes => treatmentTypes.isNotEmpty;
  bool get hasPatientId => patientId != null && patientId!.isNotEmpty;

  String get displayName => name.isNotEmpty ? name : 'مريض غير محدد';
  String get displayPatientId => patientId ?? id.substring(0, 8);
  String get displayEmail => email ?? 'غير محدد';
  String get displayPhone => phone ?? 'غير محدد';

  // Age calculation
  int get calculatedAge {
    if (hasAge) return age!;
    if (hasBirthDate) {
      final now = DateTime.now();
      int calculatedAge = now.year - birthDate!.year;
      if (now.month < birthDate!.month ||
          (now.month == birthDate!.month && now.day < birthDate!.day)) {
        calculatedAge--;
      }
      return calculatedAge;
    }
    return 0;
  }

  String get displayAge {
    final ageValue = calculatedAge;
    if (ageValue > 0) {
      return '$ageValue ${ageValue == 1 ? 'سنة' : 'سنة'}';
    }
    return 'غير محدد';
  }

  // Gender helpers
  bool get isMale =>
      gender?.toLowerCase() == 'male' || gender?.toLowerCase() == 'ذكر';
  bool get isFemale =>
      gender?.toLowerCase() == 'female' || gender?.toLowerCase() == 'أنثى';

  String get displayGender {
    if (isMale) return 'ذكر';
    if (isFemale) return 'أنثى';
    return 'غير محدد';
  }

  // Date formatting
  String get formattedBirthDate {
    if (birthDate == null) return 'غير محدد';
    return '${birthDate!.day}/${birthDate!.month}/${birthDate!.year}';
  }

  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }

  // Premium status
  String get membershipStatus => isPremium ? 'عضوية مميزة' : 'عضوية عادية';

  // Treatment types helpers
  String get treatmentTypesText {
    if (treatmentTypes.isEmpty) return 'غير محدد';
    return treatmentTypes.join(', ');
  }

  /// الحصول على أنواع العلاج بالعربية
  String get treatmentTypesTextArabic {
    if (treatmentTypes.isEmpty) return 'غير محدد';
    return treatmentTypes
        .map((type) => _getTreatmentTypeArabic(type))
        .join(', ');
  }

  /// ترجمة نوع العلاج إلى العربية
  String _getTreatmentTypeArabic(String type) {
    switch (type.toLowerCase()) {
      case 'hearing':
        return 'فحص السمع';
      case 'speech':
        return 'علاج النطق';
      case 'behavior':
        return 'تعديل السلوك';
      case 'adhd':
        return 'علاج فرط الحركة وتشتت الانتباه';
      case 'autism':
        return 'علاج طيف التوحد';
      case 'learning':
        return 'علاج صعوبات التعلم';
      case 'cognitive':
        return 'علاج معرفي';
      case 'social':
        return 'تدريب اجتماعي';
      case 'motor':
        return 'علاج حركي';
      case 'sensory':
        return 'علاج حسي';
      case 'developmental':
        return 'علاج تطويري';
      case 'communication':
        return 'علاج التواصل';
      case 'therapy':
      case 'physiotherapy':
        return 'علاج طبيعي';
      case 'occupational':
        return 'علاج وظيفي';
      case 'psychological':
        return 'علاج نفسي';
      case 'nutrition':
        return 'تغذية علاجية';
      case 'consultation':
        return 'استشارة';
      case 'follow_up':
        return 'متابعة';
      case 'assessment':
        return 'تقييم';
      case 'rehabilitation':
        return 'تأهيل';
      default:
        return type;
    }
  }

  bool hasTreatmentType(String type) {
    return treatmentTypes.any((t) => t.toLowerCase() == type.toLowerCase());
  }

  // Medical information helpers
  bool get hasMedicalInfo =>
      hasMedicalConditions || hasAllergies || hasMedications || hasSupplements;

  String get medicalSummary {
    List<String> summary = [];
    if (hasMedicalConditions) summary.add('حالات طبية');
    if (hasAllergies) summary.add('حساسية');
    if (hasMedications) summary.add('أدوية');
    if (hasSupplements) summary.add('مكملات');

    if (summary.isEmpty) return 'لا توجد معلومات طبية';
    return summary.join(' • ');
  }

  // Contact information
  bool get hasContactInfo => hasEmail || hasPhone;

  String get primaryContact {
    if (hasPhone) return phone!;
    if (hasEmail) return email!;
    return 'غير متوفر';
  }

  @override
  String toString() {
    return 'PatientModel(id: $id, name: $name, age: $displayAge, gender: $displayGender, premium: $isPremium)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PatientModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
