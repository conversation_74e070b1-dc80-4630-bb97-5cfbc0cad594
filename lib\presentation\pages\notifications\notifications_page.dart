import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_logger.dart';
import '../../../data/models/notification_model.dart';
import '../../../data/repositories/notification_repository.dart';
import 'widgets/notification_card.dart';
import 'widgets/notification_filter_chip.dart';
import 'widgets/notification_search_bar.dart';

/// صفحة الإشعارات الاحترافية
class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage>
    with TickerProviderStateMixin {
  final NotificationRepository _notificationRepository = NotificationRepository();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  List<NotificationModel> _notifications = [];
  List<NotificationModel> _filteredNotifications = [];
  Map<String, int> _stats = {};
  
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  String _selectedFilter = 'all';
  String _searchQuery = '';
  
  int _currentPage = 0;
  static const int _pageSize = 20;

  late AnimationController _refreshAnimationController;
  late Animation<double> _refreshAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadNotifications();
    _loadStats();
    _setupScrollListener();
  }

  @override
  void dispose() {
    _refreshAnimationController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _refreshAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _refreshAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _refreshAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreNotifications();
      }
    });
  }

  /// تحميل الإشعارات
  Future<void> _loadNotifications({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 0;
      _hasMoreData = true;
      _refreshAnimationController.forward();
    }

    if (!mounted) return;

    setState(() {
      if (refresh) {
        _isLoading = true;
        _notifications.clear();
      }
    });

    try {
      final notifications = await _notificationRepository.getUserNotifications(
        limit: _pageSize,
        offset: _currentPage * _pageSize,
        isRead: _selectedFilter == 'unread' ? false : 
               _selectedFilter == 'read' ? true : null,
      );

      if (mounted) {
        setState(() {
          if (refresh) {
            _notifications = notifications;
          } else {
            _notifications.addAll(notifications);
          }
          
          _hasMoreData = notifications.length == _pageSize;
          _isLoading = false;
          _isLoadingMore = false;
          
          _applyFilters();
        });
      }

      AppLogger.info(
        'Notifications loaded successfully',
        category: LogCategory.ui,
        data: {
          'count': notifications.length.toString(),
          'page': _currentPage.toString(),
          'refresh': refresh.toString(),
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
      
      AppLogger.error(
        'Failed to load notifications',
        category: LogCategory.ui,
        error: e,
      );
      
      _showErrorSnackBar('فشل في تحميل الإشعارات');
    } finally {
      if (refresh) {
        _refreshAnimationController.reverse();
      }
    }
  }

  /// تحميل المزيد من الإشعارات
  Future<void> _loadMoreNotifications() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    _currentPage++;
    await _loadNotifications();
  }

  /// تحميل إحصائيات الإشعارات
  Future<void> _loadStats() async {
    try {
      final stats = await _notificationRepository.getNotificationStats();
      if (mounted) {
        setState(() {
          _stats = stats;
        });
      }
    } catch (e) {
      AppLogger.error(
        'Failed to load notification stats',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  /// تطبيق الفلاتر والبحث
  void _applyFilters() {
    List<NotificationModel> filtered = List.from(_notifications);

    // تطبيق فلتر النوع
    if (_selectedFilter != 'all') {
      if (_selectedFilter == 'unread') {
        filtered = filtered.where((n) => !n.isRead).toList();
      } else if (_selectedFilter == 'read') {
        filtered = filtered.where((n) => n.isRead).toList();
      } else {
        filtered = filtered.where((n) => n.type == _selectedFilter).toList();
      }
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((n) =>
          n.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          n.bodyText.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
    }

    setState(() {
      _filteredNotifications = filtered;
    });
  }

  /// تحديد إشعار كمقروء
  Future<void> _markAsRead(NotificationModel notification) async {
    if (notification.isRead) return;

    try {
      final success = await _notificationRepository.markNotificationAsRead(notification.id);
      
      if (success && mounted) {
        setState(() {
          final index = _notifications.indexWhere((n) => n.id == notification.id);
          if (index != -1) {
            _notifications[index] = notification.copyWith(isRead: true);
          }
          _applyFilters();
        });
        
        // تحديث الإحصائيات
        _loadStats();
      }
    } catch (e) {
      AppLogger.error(
        'Failed to mark notification as read',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<void> _markAllAsRead() async {
    try {
      final success = await _notificationRepository.markAllNotificationsAsRead();
      
      if (success && mounted) {
        setState(() {
          _notifications = _notifications.map((n) => n.copyWith(isRead: true)).toList();
          _applyFilters();
        });
        
        _loadStats();
        _showSuccessSnackBar('تم تحديد جميع الإشعارات كمقروءة');
      }
    } catch (e) {
      AppLogger.error(
        'Failed to mark all notifications as read',
        category: LogCategory.ui,
        error: e,
      );
      _showErrorSnackBar('فشل في تحديد الإشعارات كمقروءة');
    }
  }

  /// حذف إشعار
  Future<void> _deleteNotification(NotificationModel notification) async {
    try {
      final success = await _notificationRepository.deleteNotification(notification.id);
      
      if (success && mounted) {
        setState(() {
          _notifications.removeWhere((n) => n.id == notification.id);
          _applyFilters();
        });
        
        _loadStats();
        _showSuccessSnackBar('تم حذف الإشعار');
      }
    } catch (e) {
      AppLogger.error(
        'Failed to delete notification',
        category: LogCategory.ui,
        error: e,
      );
      _showErrorSnackBar('فشل في حذف الإشعار');
    }
  }

  /// حذف الإشعارات المقروءة
  Future<void> _deleteReadNotifications() async {
    final confirmed = await _showConfirmDialog(
      title: 'حذف الإشعارات المقروءة',
      content: 'هل أنت متأكد من حذف جميع الإشعارات المقروءة؟\n\nلا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
    );

    if (confirmed == true) {
      try {
        final success = await _notificationRepository.deleteReadNotifications();
        
        if (success && mounted) {
          setState(() {
            _notifications.removeWhere((n) => n.isRead);
            _applyFilters();
          });
          
          _loadStats();
          _showSuccessSnackBar('تم حذف الإشعارات المقروءة');
        }
      } catch (e) {
        AppLogger.error(
          'Failed to delete read notifications',
          category: LogCategory.ui,
          error: e,
        );
        _showErrorSnackBar('فشل في حذف الإشعارات المقروءة');
      }
    }
  }

  /// البحث في الإشعارات
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  /// تغيير الفلتر
  void _onFilterChanged(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.white,
      elevation: 0,
      title: Text(
        'الإشعارات',
        style: TextStyle(
          fontSize: 20.sp,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios,
          color: AppColors.textPrimary,
          size: 20.sp,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        // زر تحديد الكل كمقروء
        if (_stats['unread'] != null && _stats['unread']! > 0)
          IconButton(
            icon: Icon(
              Icons.done_all,
              color: AppColors.primary,
              size: 22.sp,
            ),
            onPressed: _markAllAsRead,
            tooltip: 'تحديد الكل كمقروء',
          ),
        
        // قائمة الخيارات
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            color: AppColors.textPrimary,
            size: 22.sp,
          ),
          onSelected: (value) {
            switch (value) {
              case 'delete_read':
                _deleteReadNotifications();
                break;
              case 'refresh':
                _loadNotifications(refresh: true);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'delete_read',
              child: Row(
                children: [
                  Icon(Icons.delete_sweep, color: AppColors.error, size: 18.sp),
                  SizedBox(width: 8.w),
                  Text('حذف المقروءة', style: TextStyle(fontSize: 14.sp)),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'refresh',
              child: Row(
                children: [
                  Icon(Icons.refresh, color: AppColors.primary, size: 18.sp),
                  SizedBox(width: 8.w),
                  Text('تحديث', style: TextStyle(fontSize: 14.sp)),
                ],
              ),
            ),
          ],
        ),
      ],
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(1.h),
        child: Container(
          height: 1.h,
          color: AppColors.border,
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // شريط البحث والإحصائيات
        _buildSearchAndStats(),
        
        // فلاتر الإشعارات
        _buildFilters(),
        
        // قائمة الإشعارات
        Expanded(child: _buildNotificationsList()),
      ],
    );
  }

  Widget _buildSearchAndStats() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: AppColors.white,
      child: Column(
        children: [
          // شريط البحث
          NotificationSearchBar(
            controller: _searchController,
            onChanged: _onSearchChanged,
            onClear: () {
              _searchController.clear();
              _onSearchChanged('');
            },
          ),
          
          // الإحصائيات
          if (_stats.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildStatsRow(),
          ],
        ],
      ),
    );
  }

  Widget _buildStatsRow() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'الإجمالي',
            _stats['total']?.toString() ?? '0',
            AppColors.textPrimary,
          ),
          _buildStatItem(
            'غير مقروءة',
            _stats['unread']?.toString() ?? '0',
            AppColors.error,
          ),
          _buildStatItem(
            'مقروءة',
            _stats['read']?.toString() ?? '0',
            AppColors.success,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
        SizedBox(height: 2.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildFilters() {
    return Container(
      height: 50.h,
      color: AppColors.white,
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        children: [
          NotificationFilterChip(
            label: 'الكل',
            isSelected: _selectedFilter == 'all',
            count: _stats['total'],
            onTap: () => _onFilterChanged('all'),
          ),
          SizedBox(width: 8.w),
          NotificationFilterChip(
            label: 'غير مقروءة',
            isSelected: _selectedFilter == 'unread',
            count: _stats['unread'],
            onTap: () => _onFilterChanged('unread'),
          ),
          SizedBox(width: 8.w),
          NotificationFilterChip(
            label: 'مقروءة',
            isSelected: _selectedFilter == 'read',
            count: _stats['read'],
            onTap: () => _onFilterChanged('read'),
          ),
          SizedBox(width: 8.w),
          NotificationFilterChip(
            label: 'المواعيد',
            isSelected: _selectedFilter == 'appointment_confirmed',
            count: _stats['appointment_confirmed'],
            onTap: () => _onFilterChanged('appointment_confirmed'),
          ),
          SizedBox(width: 8.w),
          NotificationFilterChip(
            label: 'المدفوعات',
            isSelected: _selectedFilter == 'payment_received',
            count: _stats['payment_received'],
            onTap: () => _onFilterChanged('payment_received'),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_filteredNotifications.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () => _loadNotifications(refresh: true),
      color: AppColors.primary,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(16.w),
        itemCount: _filteredNotifications.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _filteredNotifications.length) {
            return _buildLoadingMoreIndicator();
          }

          final notification = _filteredNotifications[index];
          return NotificationCard(
            notification: notification,
            onTap: () => _markAsRead(notification),
            onDelete: () => _deleteNotification(notification),
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _refreshAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _refreshAnimation.value * 2 * 3.14159,
                child: SizedBox(
                  width: 40.w,
                  height: 40.h,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    color: AppColors.primary,
                  ),
                ),
              );
            },
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل الإشعارات...',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    String emptyMessage;
    String emptySubtitle;
    IconData emptyIcon;

    if (_searchQuery.isNotEmpty) {
      emptyMessage = 'لا توجد نتائج';
      emptySubtitle = 'لم نجد أي إشعارات تطابق بحثك';
      emptyIcon = Icons.search_off;
    } else if (_selectedFilter == 'unread') {
      emptyMessage = 'لا توجد إشعارات غير مقروءة';
      emptySubtitle = 'جميع إشعاراتك مقروءة';
      emptyIcon = Icons.mark_email_read;
    } else {
      emptyMessage = 'لا توجد إشعارات';
      emptySubtitle = 'لم تتلق أي إشعارات بعد';
      emptyIcon = Icons.notifications_none;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            emptyIcon,
            size: 64.sp,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            emptyMessage,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            emptySubtitle,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textLight,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: EdgeInsets.all(16.w),
      alignment: Alignment.center,
      child: SizedBox(
        width: 24.w,
        height: 24.h,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: AppColors.primary,
        ),
      ),
    );
  }

  // Helper methods
  Future<bool?> _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required String cancelText,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}