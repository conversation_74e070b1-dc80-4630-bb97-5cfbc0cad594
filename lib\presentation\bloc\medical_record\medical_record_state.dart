import 'package:equatable/equatable.dart';
import '../../../data/models/patient_model.dart';
import '../../../data/models/examination_model.dart';
import '../../../data/models/homework_model.dart';

abstract class MedicalRecordState extends Equatable {
  const MedicalRecordState();

  @override
  List<Object?> get props => [];
}

class MedicalRecordInitial extends MedicalRecordState {
  const MedicalRecordInitial();
}

class MedicalRecordLoading extends MedicalRecordState {
  const MedicalRecordLoading();
}

class MedicalRecordLoaded extends MedicalRecordState {
  final PatientModel? patient;
  final List<ExaminationModel> examinations;
  final List<HomeworkModel> homework;
  final int currentTabIndex;
  final bool isRefreshing;

  const MedicalRecordLoaded({
    this.patient,
    this.examinations = const [],
    this.homework = const [],
    this.currentTabIndex = 0,
    this.isRefreshing = false,
  });

  MedicalRecordLoaded copyWith({
    PatientModel? patient,
    List<ExaminationModel>? examinations,
    List<HomeworkModel>? homework,
    int? currentTabIndex,
    bool? isRefreshing,
  }) {
    return MedicalRecordLoaded(
      patient: patient ?? this.patient,
      examinations: examinations ?? this.examinations,
      homework: homework ?? this.homework,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  @override
  List<Object?> get props => [
        patient,
        examinations,
        homework,
        currentTabIndex,
        isRefreshing,
      ];
}

class PatientInfoLoaded extends MedicalRecordState {
  final PatientModel patient;

  const PatientInfoLoaded(this.patient);

  @override
  List<Object?> get props => [patient];
}

class ExaminationsLoaded extends MedicalRecordState {
  final List<ExaminationModel> examinations;

  const ExaminationsLoaded(this.examinations);

  @override
  List<Object?> get props => [examinations];
}

class HomeworkLoaded extends MedicalRecordState {
  final List<HomeworkModel> homework;

  const HomeworkLoaded(this.homework);

  @override
  List<Object?> get props => [homework];
}

class TabChanged extends MedicalRecordState {
  final int tabIndex;

  const TabChanged(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

class MedicalRecordError extends MedicalRecordState {
  final String message;
  final String? errorCode;

  const MedicalRecordError(this.message, {this.errorCode});

  @override
  List<Object?> get props => [message, errorCode];
}

class MedicalRecordEmpty extends MedicalRecordState {
  final String message;

  const MedicalRecordEmpty(this.message);

  @override
  List<Object?> get props => [message];
}