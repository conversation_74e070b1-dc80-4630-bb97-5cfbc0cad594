part of 'appointment_bloc.dart';

/// أحداث المواعيد
abstract class AppointmentEvent extends Equatable {
  const AppointmentEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل المواعيد
class LoadAppointments extends AppointmentEvent {
  final String patientId;
  final String? status;
  final DateTime? fromDate;
  final DateTime? toDate;
  final int? limit;
  final int? offset;

  const LoadAppointments({
    required this.patientId,
    this.status,
    this.fromDate,
    this.toDate,
    this.limit,
    this.offset,
  });

  @override
  List<Object?> get props => [patientId, status, fromDate, toDate, limit, offset];
}

/// إنشاء موعد جديد
class CreateAppointment extends AppointmentEvent {
  final String patientId;
  final DateTime appointmentDate;
  final String timeSlotId;
  final String appointmentType;
  final String? notes;
  final String? employeeId;
  final int? durationMinutes;
  final double? consultationFee;

  const CreateAppointment({
    required this.patientId,
    required this.appointmentDate,
    required this.timeSlotId,
    required this.appointmentType,
    this.notes,
    this.employeeId,
    this.durationMinutes,
    this.consultationFee,
  });

  @override
  List<Object?> get props => [
    patientId, 
    appointmentDate, 
    timeSlotId, 
    appointmentType, 
    notes, 
    employeeId,
    durationMinutes,
    consultationFee,
  ];
}

/// تحديث موعد
class UpdateAppointment extends AppointmentEvent {
  final String appointmentId;
  final DateTime? appointmentDate;
  final String? timeSlotId;
  final String? appointmentType;
  final String? status;
  final String? notes;
  final String? employeeId;
  final int? durationMinutes;
  final double? consultationFee;
  final double? paidAmount;

  const UpdateAppointment({
    required this.appointmentId,
    this.appointmentDate,
    this.timeSlotId,
    this.appointmentType,
    this.status,
    this.notes,
    this.employeeId,
    this.durationMinutes,
    this.consultationFee,
    this.paidAmount,
  });

  @override
  List<Object?> get props => [
    appointmentId, 
    appointmentDate, 
    timeSlotId, 
    appointmentType, 
    status, 
    notes, 
    employeeId,
    durationMinutes,
    consultationFee,
    paidAmount,
  ];
}

/// إلغاء موعد
class CancelAppointment extends AppointmentEvent {
  final String appointmentId;
  final String? reason;

  const CancelAppointment({
    required this.appointmentId,
    this.reason,
  });

  @override
  List<Object?> get props => [appointmentId, reason];
}

/// حذف موعد
class DeleteAppointment extends AppointmentEvent {
  final String appointmentId;

  const DeleteAppointment({required this.appointmentId});

  @override
  List<Object?> get props => [appointmentId];
}

/// تحميل الأوقات المتاحة
class LoadAvailableTimeSlots extends AppointmentEvent {
  final DateTime date;

  const LoadAvailableTimeSlots({required this.date});

  @override
  List<Object?> get props => [date];
}

/// البحث في المواعيد
class SearchAppointments extends AppointmentEvent {
  final String patientId;
  final String? searchTerm;
  final String? status;
  final String? appointmentType;

  const SearchAppointments({
    required this.patientId,
    this.searchTerm,
    this.status,
    this.appointmentType,
  });

  @override
  List<Object?> get props => [patientId, searchTerm, status, appointmentType];
}

/// تحميل إحصائيات المواعيد
class LoadAppointmentStats extends AppointmentEvent {
  final String patientId;

  const LoadAppointmentStats({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

/// تحديث المواعيد
class RefreshAppointments extends AppointmentEvent {
  final String patientId;
  final String? status;
  final DateTime? fromDate;
  final DateTime? toDate;
  final int? limit;
  final int? offset;

  const RefreshAppointments({
    required this.patientId,
    this.status,
    this.fromDate,
    this.toDate,
    this.limit,
    this.offset,
  });

  @override
  List<Object?> get props => [patientId, status, fromDate, toDate, limit, offset];
}