class ClinicInfoModel {
  final String id;
  final String key;
  final String value;
  final String? description;
  final String dataType;
  final bool isPublic;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isActive;
  final int displayOrder;
  final String infoType;
  final String? displayName;
  final String? infoValue;
  final String? iconName;

  ClinicInfoModel({
    required this.id,
    required this.key,
    required this.value,
    this.description,
    this.dataType = 'string',
    this.isPublic = false,
    this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.displayOrder = 0,
    this.infoType = 'general',
    this.displayName,
    this.infoValue,
    this.iconName,
  });

  factory ClinicInfoModel.fromJson(Map<String, dynamic> json) {
    return ClinicInfoModel(
      id: json['id'] ?? '',
      key: json['key'] ?? '',
      value: json['value'] ?? '',
      description: json['description'],
      dataType: json['data_type'] ?? 'string',
      isPublic: json['is_public'] ?? false,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      isActive: json['is_active'] ?? true,
      displayOrder: json['display_order'] ?? 0,
      infoType: json['info_type'] ?? 'general',
      displayName: json['display_name'],
      infoValue: json['info_value'],
      iconName: json['icon_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'key': key,
      'value': value,
      'description': description,
      'data_type': dataType,
      'is_public': isPublic,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_active': isActive,
      'display_order': displayOrder,
      'info_type': infoType,
      'display_name': displayName,
      'info_value': infoValue,
      'icon_name': iconName,
    };
  }

  ClinicInfoModel copyWith({
    String? id,
    String? key,
    String? value,
    String? description,
    String? dataType,
    bool? isPublic,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    int? displayOrder,
    String? infoType,
    String? displayName,
    String? infoValue,
    String? iconName,
  }) {
    return ClinicInfoModel(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      dataType: dataType ?? this.dataType,
      isPublic: isPublic ?? this.isPublic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      displayOrder: displayOrder ?? this.displayOrder,
      infoType: infoType ?? this.infoType,
      displayName: displayName ?? this.displayName,
      infoValue: infoValue ?? this.infoValue,
      iconName: iconName ?? this.iconName,
    );
  }

  // Helper methods
  bool get hasDescription => description != null && description!.isNotEmpty;
  bool get hasDisplayName => displayName != null && displayName!.isNotEmpty;
  bool get hasInfoValue => infoValue != null && infoValue!.isNotEmpty;
  bool get hasIcon => iconName != null && iconName!.isNotEmpty;
  
  String get displayKey => key.isNotEmpty ? key : 'غير محدد';
  String get displayValue => value.isNotEmpty ? value : 'غير محدد';
  String get displayDescription => description ?? 'لا يوجد وصف';
  String get displayNameText => displayName ?? key;
  String get displayInfoValue => infoValue ?? value;
  
  // Data type helpers
  bool get isStringType => dataType.toLowerCase() == 'string';
  bool get isNumberType => dataType.toLowerCase() == 'number';
  bool get isBooleanType => dataType.toLowerCase() == 'boolean';
  bool get isDateType => dataType.toLowerCase() == 'date';
  bool get isEmailType => dataType.toLowerCase() == 'email';
  bool get isPhoneType => dataType.toLowerCase() == 'phone';
  bool get isUrlType => dataType.toLowerCase() == 'url';
  
  String get dataTypeText {
    switch (dataType.toLowerCase()) {
      case 'string':
        return 'نص';
      case 'number':
        return 'رقم';
      case 'boolean':
        return 'منطقي';
      case 'date':
        return 'تاريخ';
      case 'email':
        return 'بريد إلكتروني';
      case 'phone':
        return 'هاتف';
      case 'url':
        return 'رابط';
      default:
        return 'غير محدد';
    }
  }
  
  // Info type helpers
  bool get isGeneralInfo => infoType.toLowerCase() == 'general';
  bool get isContactInfo => infoType.toLowerCase() == 'contact';
  bool get isAddressInfo => infoType.toLowerCase() == 'address';
  bool get isWorkingHours => infoType.toLowerCase() == 'working_hours';
  bool get isServiceInfo => infoType.toLowerCase() == 'service';
  bool get isSettingInfo => infoType.toLowerCase() == 'setting';
  
  String get infoTypeText {
    switch (infoType.toLowerCase()) {
      case 'general':
        return 'عام';
      case 'contact':
        return 'اتصال';
      case 'address':
        return 'عنوان';
      case 'working_hours':
        return 'ساعات العمل';
      case 'service':
        return 'خدمة';
      case 'setting':
        return 'إعداد';
      default:
        return 'غير محدد';
    }
  }
  
  // Value parsing based on data type
  dynamic get parsedValue {
    switch (dataType.toLowerCase()) {
      case 'number':
        return double.tryParse(value) ?? int.tryParse(value) ?? 0;
      case 'boolean':
        return value.toLowerCase() == 'true' || value == '1';
      case 'date':
        return DateTime.tryParse(value);
      default:
        return value;
    }
  }
  
  // Formatted value for display
  String get formattedValue {
    switch (dataType.toLowerCase()) {
      case 'boolean':
        final boolValue = parsedValue as bool;
        return boolValue ? 'نعم' : 'لا';
      case 'date':
        final dateValue = parsedValue as DateTime?;
        if (dateValue != null) {
          return '${dateValue.day}/${dateValue.month}/${dateValue.year}';
        }
        return value;
      case 'phone':
        return value.replaceAllMapped(
          RegExp(r'(\d{3})(\d{3})(\d{4})'),
          (match) => '${match[1]}-${match[2]}-${match[3]}',
        );
      default:
        return value;
    }
  }
  
  // Status helpers
  String get statusText => isActive ? 'نشط' : 'غير نشط';
  String get visibilityText => isPublic ? 'عام' : 'خاص';
  
  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }
  
  // Common clinic info keys
  static const String clinicName = 'clinic_name';
  static const String clinicPhone = 'clinic_phone';
  static const String clinicEmail = 'clinic_email';
  static const String clinicAddress = 'clinic_address';
  static const String workingHours = 'working_hours';
  static const String emergencyPhone = 'emergency_phone';
  static const String website = 'website';
  static const String socialMedia = 'social_media';
  
  bool get isClinicName => key == clinicName;
  bool get isClinicPhone => key == clinicPhone;
  bool get isClinicEmail => key == clinicEmail;
  bool get isClinicAddress => key == clinicAddress;
  bool get isWorkingHoursInfo => key == workingHours;
  bool get isEmergencyPhone => key == emergencyPhone;
  bool get isWebsite => key == website;
  bool get isSocialMedia => key == socialMedia;
  
  @override
  String toString() {
    return 'ClinicInfoModel(id: $id, key: $key, value: $displayValue, type: $infoTypeText)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ClinicInfoModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
