import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import '../utils/app_logger.dart';

/// خدمة الإشعارات المحلية
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  
  // Callback للنقر على الإشعار
  Function(dynamic)? onNotificationTapped;

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info(
        '🔔 Initializing notification service',
        category: LogCategory.general,
      );

      // إعدادات Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // إعدادات عامة
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // تهيئة الإشعارات
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      _isInitialized = true;

      AppLogger.info(
        '✅ Notification service initialized successfully',
        category: LogCategory.general,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize notification service',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// معالج النقر على الإشعار
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    AppLogger.info(
      '👆 Notification tapped',
      category: LogCategory.general,
      data: {
        'id': notificationResponse.id.toString(),
        'payload': notificationResponse.payload ?? 'No payload',
      },
    );

    // يمكن إضافة منطق التنقل هنا
  }

  /// عرض إشعار فوري
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'iihc_channel',
        'IIHC Notifications',
        channelDescription: 'إشعارات تطبيق IIHC',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: false,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails();

      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );

      AppLogger.info(
        '📱 Notification shown',
        category: LogCategory.general,
        data: {
          'id': id.toString(),
          'title': title,
          'body': body,
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to show notification',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// جدولة إشعار
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'iihc_scheduled_channel',
        'IIHC Scheduled Notifications',
        channelDescription: 'إشعارات مجدولة لتطبيق IIHC',
        importance: Importance.max,
        priority: Priority.high,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails();

      const NotificationDetails platformChannelSpecifics =
          NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // تحويل DateTime إلى TZDateTime
      final tz.TZDateTime tzScheduledDate = tz.TZDateTime.from(scheduledDate, tz.local);
      
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tzScheduledDate,
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload,
      );

      AppLogger.info(
        '⏰ Notification scheduled',
        category: LogCategory.general,
        data: {
          'id': id.toString(),
          'title': title,
          'scheduledDate': scheduledDate.toIso8601String(),
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to schedule notification',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// إلغاء إشعار مجدول
  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);

      AppLogger.info(
        '🗑️ Notification cancelled',
        category: LogCategory.general,
        data: {'id': id.toString()},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to cancel notification',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();

      AppLogger.info(
        '🗑️ All notifications cancelled',
        category: LogCategory.general,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to cancel all notifications',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// طلب الأذونات (Android 13+)
  Future<bool> requestPermissions() async {
    try {
      if (defaultTargetPlatform == TargetPlatform.android) {
        final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
            _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        final bool? granted = await androidImplementation?.requestNotificationsPermission();
        return granted ?? false;
      }
      return true; // iOS permissions are handled during initialization
    } catch (e) {
      AppLogger.error(
        '❌ Failed to request notification permissions',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// إشعار تذكير بالموعد
  Future<void> showAppointmentReminder({
    required String appointmentId,
    required String patientName,
    required DateTime appointmentDate,
    required String appointmentTime,
  }) async {
    await showNotification(
      id: appointmentId.hashCode,
      title: 'تذكير بالموعد',
      body: 'لديك موعد اليوم في $appointmentTime',
      payload: 'appointment:$appointmentId',
    );
  }

  /// جدولة تذكير بالموعد
  Future<void> scheduleAppointmentReminder({
    required String appointmentId,
    required String patientName,
    required DateTime appointmentDate,
    required String appointmentTime,
    Duration reminderBefore = const Duration(hours: 1),
  }) async {
    final reminderTime = appointmentDate.subtract(reminderBefore);
    
    // لا نجدول تذكيرات للمواعيد الماضية
    if (reminderTime.isBefore(DateTime.now())) {
      return;
    }

    await scheduleNotification(
      id: appointmentId.hashCode,
      title: 'تذكير بالموعد',
      body: 'لديك موعد خلال ${reminderBefore.inHours} ساعة في $appointmentTime',
      scheduledDate: reminderTime,
      payload: 'appointment:$appointmentId',
    );
  }

  /// إشعار واجب جديد
  Future<void> showNewHomeworkNotification({
    required String homeworkId,
    required String homeworkTitle,
  }) async {
    await showNotification(
      id: homeworkId.hashCode,
      title: 'واجب جديد',
      body: 'تم إضافة واجب جديد: $homeworkTitle',
      payload: 'homework:$homeworkId',
    );
  }

  /// إشعار نتيجة فحص
  Future<void> showExaminationResultNotification({
    required String examinationId,
    required String examinationTitle,
  }) async {
    await showNotification(
      id: examinationId.hashCode,
      title: 'نتيجة فحص جديدة',
      body: 'تم إضافة نتيجة فحص: $examinationTitle',
      payload: 'examination:$examinationId',
    );
  }
}