import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/appointment_model.dart';
import '../../core/utils/app_logger.dart';

/// Repository للتعامل مع بيانات المواعيد في Supabase
class AppointmentRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// إنشاء موعد جديد
  Future<AppointmentModel> createAppointment({
    required String patientId,
    required DateTime appointmentDate,
    required String timeSlotId,
    required String appointmentType,
    String? notes,
    String? employeeId,
    int durationMinutes = 30,
    double consultationFee = 0.0,
  }) async {
    try {
      AppLogger.info(
        '📅 Creating new appointment',
        category: LogCategory.data,
        data: {
          'patientId': patientId,
          'appointmentDate': appointmentDate.toIso8601String(),
          'appointmentType': appointmentType,
        },
      );

      final response = await _supabase
          .from('appointments')
          .insert({
            'patient_id': patientId,
            'appointment_date': appointmentDate.toIso8601String(),
            'time_slot_id': timeSlotId,
            'appointment_type': appointmentType,
            'notes': notes,
            'employee_id': employeeId,
            'status': 'booked',
            'duration_minutes': durationMinutes,
            'consultation_fee': consultationFee,
            'paid_amount': 0.0,
            'remaining_amount': consultationFee,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();

      final appointment = AppointmentModel.fromJson(response);
      
      AppLogger.info(
        '✅ Appointment created successfully',
        category: LogCategory.data,
        data: {'appointmentId': appointment.id},
      );

      return appointment;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to create appointment',
        category: LogCategory.data,
        error: e,
      );
      rethrow;
    }
  }

  /// الحصول على مواعيد المريض
  Future<List<AppointmentModel>> getPatientAppointments({
    String? patientId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info(
        '🔍 Fetching patient appointments',
        category: LogCategory.data,
        data: {
          'patientId': patientId ?? 'all',
          'status': status ?? 'all',
        },
      );

      var query = _supabase.from('appointments').select();

      if (patientId != null) {
        query = query.eq('patient_id', patientId);
      }

      if (status != null) {
        query = query.eq('status', status);
      }

      if (fromDate != null) {
        query = query.gte('appointment_date', fromDate.toIso8601String().split('T')[0]);
      }

      if (toDate != null) {
        query = query.lte('appointment_date', toDate.toIso8601String().split('T')[0]);
      }

      var orderedQuery = query.order('appointment_date', ascending: false);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await orderedQuery;

      final appointments = response.map((json) => AppointmentModel.fromJson(json)).toList();
      
      AppLogger.info(
        '✅ Fetched ${appointments.length} appointments',
        category: LogCategory.data,
        data: {'count': appointments.length.toString()},
      );

      return appointments;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to fetch appointments',
        category: LogCategory.data,
        error: e,
      );
      rethrow;
    }
  }

  /// الحصول على موعد بواسطة ID
  Future<AppointmentModel?> getAppointmentById(String appointmentId) async {
    try {
      AppLogger.info(
        '🔍 Fetching appointment by ID',
        category: LogCategory.data,
        data: {'appointmentId': appointmentId},
      );

      final response = await _supabase
          .from('appointments')
          .select()
          .eq('id', appointmentId)
          .maybeSingle();

      if (response == null) {
        AppLogger.info(
          '❌ Appointment not found',
          category: LogCategory.data,
          data: {'appointmentId': appointmentId},
        );
        return null;
      }

      final appointment = AppointmentModel.fromJson(response);
      
      AppLogger.info(
        '✅ Appointment found',
        category: LogCategory.data,
        data: {'appointmentId': appointment.id},
      );

      return appointment;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to fetch appointment',
        category: LogCategory.data,
        error: e,
      );
      rethrow;
    }
  }

  /// تحديث موعد
  Future<AppointmentModel> updateAppointment({
    required String appointmentId,
    DateTime? appointmentDate,
    String? timeSlotId,
    String? appointmentType,
    String? status,
    String? notes,
    String? employeeId,
    int? durationMinutes,
    double? consultationFee,
    double? paidAmount,
  }) async {
    try {
      AppLogger.info(
        '📝 Updating appointment',
        category: LogCategory.data,
        data: {'appointmentId': appointmentId},
      );

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (appointmentDate != null) {
        updateData['appointment_date'] = appointmentDate.toIso8601String();
      }
      if (timeSlotId != null) updateData['time_slot_id'] = timeSlotId;
      if (appointmentType != null) updateData['appointment_type'] = appointmentType;
      if (status != null) updateData['status'] = status;
      if (notes != null) updateData['notes'] = notes;
      if (employeeId != null) updateData['employee_id'] = employeeId;
      if (durationMinutes != null) updateData['duration_minutes'] = durationMinutes;
      if (consultationFee != null) {
        updateData['consultation_fee'] = consultationFee;
        // تحديث المبلغ المتبقي
        final currentPaid = paidAmount ?? 0.0;
        updateData['remaining_amount'] = consultationFee - currentPaid;
      }
      if (paidAmount != null) {
        updateData['paid_amount'] = paidAmount;
        // تحديث المبلغ المتبقي
        final currentFee = consultationFee ?? 0.0;
        updateData['remaining_amount'] = currentFee - paidAmount;
      }

      final response = await _supabase
          .from('appointments')
          .update(updateData)
          .eq('id', appointmentId)
          .select()
          .single();

      final appointment = AppointmentModel.fromJson(response);
      
      AppLogger.info(
        '✅ Appointment updated successfully',
        category: LogCategory.data,
        data: {'appointmentId': appointment.id},
      );

      return appointment;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to update appointment',
        category: LogCategory.data,
        error: e,
      );
      rethrow;
    }
  }

  /// إلغاء موعد
  Future<AppointmentModel> cancelAppointment(String appointmentId, {String? reason}) async {
    try {
      AppLogger.info(
        '🗑️ Cancelling appointment',
        category: LogCategory.data,
        data: {
          'appointmentId': appointmentId,
          'reason': reason ?? 'No reason provided',
        },
      );

      final updateData = {
        'status': 'cancelled',
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (reason != null) {
        updateData['notes'] = reason;
      }

      final response = await _supabase
          .from('appointments')
          .update(updateData)
          .eq('id', appointmentId)
          .select()
          .single();

      final appointment = AppointmentModel.fromJson(response);
      
      AppLogger.info(
        '✅ Appointment cancelled successfully',
        category: LogCategory.data,
        data: {'appointmentId': appointment.id},
      );

      return appointment;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to cancel appointment',
        category: LogCategory.data,
        error: e,
      );
      rethrow;
    }
  }

  /// حذف موعد
  Future<void> deleteAppointment(String appointmentId) async {
    try {
      AppLogger.info(
        '🗑️ Deleting appointment',
        category: LogCategory.data,
        data: {'appointmentId': appointmentId},
      );

      await _supabase
          .from('appointments')
          .delete()
          .eq('id', appointmentId);

      AppLogger.info(
        '✅ Appointment deleted successfully',
        category: LogCategory.data,
        data: {'appointmentId': appointmentId},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to delete appointment',
        category: LogCategory.data,
        error: e,
      );
      rethrow;
    }
  }

  /// الحصول على الأوقات المتاحة لتاريخ معين
  Future<List<String>> getAvailableTimeSlots(DateTime date) async {
    try {
      AppLogger.info(
        '🕐 Fetching available time slots',
        category: LogCategory.data,
        data: {'date': date.toIso8601String().split('T')[0]},
      );

      // الحصول على الأوقات المحجوزة لهذا التاريخ
      final bookedSlots = await _supabase
          .from('appointments')
          .select('time_slot_id')
          .eq('appointment_date', date.toIso8601String().split('T')[0])
          .neq('status', 'cancelled');

      final bookedTimeSlotIds = bookedSlots.map((slot) => slot['time_slot_id'] as String?).where((id) => id != null).toList();

      // الحصول على جميع الأوقات المتاحة من جدول time_slots
      final allTimeSlots = await _supabase
          .from('time_slots')
          .select('id, start_time, end_time')
          .eq('is_active', true)
          .order('start_time', ascending: true);

      // تصفية الأوقات المحجوزة
      final availableSlots = allTimeSlots
          .where((slot) => !bookedTimeSlotIds.contains(slot['id']))
          .map((slot) => '${slot['start_time']} - ${slot['end_time']}')
          .toList();

      AppLogger.info(
        '✅ Found ${availableSlots.length} available time slots',
        category: LogCategory.data,
        data: {'count': availableSlots.length.toString()},
      );

      return availableSlots;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to fetch available time slots',
        category: LogCategory.data,
        error: e,
      );
      
      // إرجاع أوقات افتراضية في حالة الخطأ
      return [
        '09:00 - 09:30',
        '09:30 - 10:00',
        '10:00 - 10:30',
        '10:30 - 11:00',
        '11:00 - 11:30',
        '11:30 - 12:00',
        '14:00 - 14:30',
        '14:30 - 15:00',
        '15:00 - 15:30',
        '15:30 - 16:00',
        '16:00 - 16:30',
        '16:30 - 17:00',
      ];
    }
  }

  /// الحصول على إحصائيات المواعيد
  Future<Map<String, int>> getAppointmentStats(String patientId) async {
    try {
      AppLogger.info(
        '📊 Fetching appointment stats',
        category: LogCategory.data,
        data: {'patientId': patientId},
      );

      final response = await _supabase
          .from('appointments')
          .select('status')
          .eq('patient_id', patientId);

      final stats = <String, int>{
        'total': response.length,
        'booked': 0,
        'completed': 0,
        'cancelled': 0,
        'no_show': 0,
      };

      for (final appointment in response) {
        final status = appointment['status'] as String;
        stats[status] = (stats[status] ?? 0) + 1;
      }

      AppLogger.info(
        '✅ Appointment stats calculated',
        category: LogCategory.data,
        data: stats.map((key, value) => MapEntry(key, value.toString())),
      );

      return stats;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to fetch appointment stats',
        category: LogCategory.data,
        error: e,
      );
      rethrow;
    }
  }

  /// البحث في المواعيد
  Future<List<AppointmentModel>> searchAppointments({
    required String patientId,
    String? searchTerm,
    String? status,
    String? appointmentType,
  }) async {
    try {
      AppLogger.info(
        '🔍 Searching appointments',
        category: LogCategory.data,
        data: {
          'patientId': patientId,
          'searchTerm': searchTerm ?? 'none',
          'status': status ?? 'all',
          'type': appointmentType ?? 'all',
        },
      );

      var query = _supabase
          .from('appointments')
          .select()
          .eq('patient_id', patientId);

      if (status != null) {
        query = query.eq('status', status);
      }

      if (appointmentType != null) {
        query = query.eq('appointment_type', appointmentType);
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        query = query.or('notes.ilike.%$searchTerm%,appointment_type.ilike.%$searchTerm%');
      }

      final response = await query.order('appointment_date', ascending: false);

      final appointments = response.map((json) => AppointmentModel.fromJson(json)).toList();
      
      AppLogger.info(
        '✅ Found ${appointments.length} appointments',
        category: LogCategory.data,
        data: {'count': appointments.length.toString()},
      );

      return appointments;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to search appointments',
        category: LogCategory.data,
        error: e,
      );
      rethrow;
    }
  }
}
