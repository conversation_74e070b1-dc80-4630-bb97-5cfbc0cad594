part of 'notifications_bloc.dart';

/// أحداث الإشعارات
abstract class NotificationsEvent extends Equatable {
  const NotificationsEvent();

  @override
  List<Object?> get props => [];
}

/// تهيئة خدمات الإشعارات
class InitializeNotifications extends NotificationsEvent {
  const InitializeNotifications();
}

/// طلب أذونات الإشعارات
class RequestNotificationPermissions extends NotificationsEvent {
  const RequestNotificationPermissions();
}

/// عرض إشعار فوري
class ShowNotification extends NotificationsEvent {
  final int id;
  final String title;
  final String body;
  final String? payload;

  const ShowNotification({
    required this.id,
    required this.title,
    required this.body,
    this.payload,
  });

  @override
  List<Object?> get props => [id, title, body, payload];
}

/// جدولة إشعار
class ScheduleNotification extends NotificationsEvent {
  final int id;
  final String title;
  final String body;
  final DateTime scheduledDate;
  final String? payload;

  const ScheduleNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.scheduledDate,
    this.payload,
  });

  @override
  List<Object?> get props => [id, title, body, scheduledDate, payload];
}

/// إلغاء إشعار
class CancelNotification extends NotificationsEvent {
  final int id;

  const CancelNotification({required this.id});

  @override
  List<Object?> get props => [id];
}

/// إلغاء جميع الإشعارات
class CancelAllNotifications extends NotificationsEvent {
  const CancelAllNotifications();
}

/// تحديث إعدادات الإشعارات
class UpdateNotificationSettings extends NotificationsEvent {
  final bool enableAppointmentReminders;
  final bool enableHomeworkNotifications;
  final bool enableExaminationResults;

  const UpdateNotificationSettings({
    required this.enableAppointmentReminders,
    required this.enableHomeworkNotifications,
    required this.enableExaminationResults,
  });

  @override
  List<Object?> get props => [
    enableAppointmentReminders,
    enableHomeworkNotifications,
    enableExaminationResults,
  ];
}

/// الاشتراك في موضوع FCM
class SubscribeToTopic extends NotificationsEvent {
  final String topic;

  const SubscribeToTopic({required this.topic});

  @override
  List<Object?> get props => [topic];
}

/// إلغاء الاشتراك من موضوع FCM
class UnsubscribeFromTopic extends NotificationsEvent {
  final String topic;

  const UnsubscribeFromTopic({required this.topic});

  @override
  List<Object?> get props => [topic];
}

/// جدولة تذكير بالموعد
class ScheduleAppointmentReminder extends NotificationsEvent {
  final String appointmentId;
  final String patientName;
  final DateTime appointmentDate;
  final String appointmentTime;
  final Duration reminderBefore;

  const ScheduleAppointmentReminder({
    required this.appointmentId,
    required this.patientName,
    required this.appointmentDate,
    required this.appointmentTime,
    this.reminderBefore = const Duration(hours: 1),
  });

  @override
  List<Object?> get props => [
    appointmentId,
    patientName,
    appointmentDate,
    appointmentTime,
    reminderBefore,
  ];
}

/// إشعار واجب جديد
class ShowNewHomeworkNotification extends NotificationsEvent {
  final String homeworkId;
  final String homeworkTitle;

  const ShowNewHomeworkNotification({
    required this.homeworkId,
    required this.homeworkTitle,
  });

  @override
  List<Object?> get props => [homeworkId, homeworkTitle];
}

/// إشعار نتيجة فحص
class ShowExaminationResultNotification extends NotificationsEvent {
  final String examinationId;
  final String examinationTitle;

  const ShowExaminationResultNotification({
    required this.examinationId,
    required this.examinationTitle,
  });

  @override
  List<Object?> get props => [examinationId, examinationTitle];
}