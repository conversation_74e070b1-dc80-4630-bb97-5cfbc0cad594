import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';
import '../utils/app_logger.dart';
import '../constants/app_constants.dart';

/// خدمة إدارة FCM Tokens
class FCMTokenService {
  static final FCMTokenService _instance = FCMTokenService._internal();
  factory FCMTokenService() => _instance;
  FCMTokenService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// حفظ FCM Token جديد
  Future<bool> saveToken(String userId, String fcmToken) async {
    try {
      AppLogger.info(
        '💾 Attempting to save FCM token',
        category: LogCategory.fcm,
        data: {'userId': userId, 'tokenLength': fcmToken.length},
      );

      // التأكد من إنشاء الجدول أولاً
      await _ensureTableExists();

      final deviceInfo = await _getDeviceInfo();
      
      // التحقق من وجود token موجود لنفس المستخدم
      final existingTokens = await _supabase
          .from('user_fcm_tokens')
          .select()
          .eq('user_id', userId)
          .eq('fcm_token', fcmToken);

      if (existingTokens.isNotEmpty) {
        // تحديث token موجود
        await _supabase
            .from('user_fcm_tokens')
            .update({
              'device_info': deviceInfo,
              'is_active': true,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('user_id', userId)
            .eq('fcm_token', fcmToken);
        
        AppLogger.info(
          '🔄 FCM Token updated',
          category: LogCategory.fcm,
          data: {'userId': userId, 'platform': deviceInfo['platform']},
        );
      } else {
        // إنشاء token جديد
        await _supabase
            .from('user_fcm_tokens')
            .insert({
              'user_id': userId,
              'fcm_token': fcmToken,
              'device_info': deviceInfo,
              'is_active': true,
            });
        
        AppLogger.info(
          '✅ FCM Token saved',
          category: LogCategory.fcm,
          data: {'userId': userId, 'platform': deviceInfo['platform']},
        );
      }

      return true;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save FCM token',
        category: LogCategory.fcm,
        error: e,
        data: {'userId': userId},
      );
      return false;
    }
  }

  /// تحديث FCM Token
  Future<bool> updateToken(String userId, String oldToken, String newToken) async {
    try {
      final deviceInfo = await _getDeviceInfo();
      
      await _supabase
          .from('user_fcm_tokens')
          .update({
            'fcm_token': newToken,
            'device_info': deviceInfo,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('fcm_token', oldToken);

      AppLogger.info(
        '🔄 FCM Token refreshed',
        category: LogCategory.fcm,
        data: {'userId': userId},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to update FCM token',
        category: LogCategory.fcm,
        error: e,
      );
      return false;
    }
  }

  /// حذف FCM Token
  Future<bool> deleteToken(String userId, String? fcmToken) async {
    try {
      if (fcmToken != null) {
        // حذف token محدد
        await _supabase
            .from('user_fcm_tokens')
            .delete()
            .eq('user_id', userId)
            .eq('fcm_token', fcmToken);
      } else {
        // حذف جميع tokens للمستخدم
        await _supabase
            .from('user_fcm_tokens')
            .delete()
            .eq('user_id', userId);
      }

      AppLogger.info(
        '🗑️ FCM Token deleted',
        category: LogCategory.fcm,
        data: {'userId': userId, 'specificToken': fcmToken != null},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to delete FCM token',
        category: LogCategory.fcm,
        error: e,
      );
      return false;
    }
  }

  /// الحصول على FCM Token الحالي
  Future<String?> getCurrentToken() async {
    try {
      return await _messaging.getToken();
    } catch (e) {
      AppLogger.error(
        'Failed to get current FCM token',
        category: LogCategory.fcm,
        error: e,
      );
      return null;
    }
  }

  /// تنظيف tokens غير النشطة
  Future<void> cleanupInactiveTokens(String userId) async {
    try {
      await _supabase
          .from('user_fcm_tokens')
          .delete()
          .eq('user_id', userId)
          .eq('is_active', false);

      AppLogger.info(
        '🧹 Inactive FCM tokens cleaned up',
        category: LogCategory.fcm,
        data: {'userId': userId},
      );
    } catch (e) {
      AppLogger.error(
        'Failed to cleanup inactive tokens',
        category: LogCategory.fcm,
        error: e,
      );
    }
  }

  /// التأكد من وجود جدول user_fcm_tokens
  Future<void> _ensureTableExists() async {
    try {
      // محاولة إنشاء الجدول إذا لم يكن موجود
      await _supabase.rpc('create_fcm_tokens_table_if_not_exists');
      
      AppLogger.info(
        '✅ FCM tokens table ensured',
        category: LogCategory.fcm,
      );
    } catch (e) {
      // تجاهل الخطأ إذا كان الجدول موجود بالفعل أو إذا كانت الدالة غير موجودة
      AppLogger.debug(
        'FCM tokens table might already exist or function not available',
        category: LogCategory.fcm,
      );
    }
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, String>> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return {
          'platform': 'android',
          'version': '${androidInfo.model}_${androidInfo.version.release}',
          'app_version': AppConstants.appVersion,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return {
          'platform': 'ios',
          'version': '${iosInfo.model}_${iosInfo.systemVersion}',
          'app_version': AppConstants.appVersion,
        };
      } else {
        return {
          'platform': 'unknown',
          'version': 'unknown',
          'app_version': AppConstants.appVersion,
        };
      }
    } catch (e) {
      AppLogger.error(
        'Failed to get device info',
        category: LogCategory.fcm,
        error: e,
      );
      return {
        'platform': 'error',
        'version': 'error',
        'app_version': AppConstants.appVersion,
      };
    }
  }
}
