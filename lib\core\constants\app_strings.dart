/// نصوص التطبيق باللغة العربية
class AppStrings {
  AppStrings._();

  // عام
  static const String appName = 'IIHC';
  static const String clinicName =
      'مركز مستشفى إربد الإسلامي للسمع والنطق والسلوك';
  static const String loading = 'جاري التحميل...';
  static const String error = 'حدث خطأ';
  static const String success = 'تم بنجاح';
  static const String retry = 'إعادة المحاولة';
  static const String cancel = 'إلغاء';
  static const String confirm = 'تأكيد';
  static const String save = 'حفظ';
  static const String edit = 'تعديل';
  static const String delete = 'حذف';
  static const String add = 'إضافة';
  static const String search = 'بحث';
  static const String filter = 'تصفية';
  static const String sort = 'ترتيب';
  static const String noData = 'لا توجد بيانات';
  static const String noResults = 'لا توجد نتائج';

  // تسجيل الدخول والحساب
  static const String login = 'تسجيل الدخول';
  static const String logout = 'تسجيل الخروج';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String name = 'الاسم';
  static const String phone = 'رقم الهاتف';
  static const String forgotPassword = 'نسيت كلمة المرور؟';

  // التنقل
  static const String home = 'الرئيسية';
  static const String appointments = 'المواعيد';
  static const String products = 'المنتجات';
  static const String profile = 'شخصي';
  static const String medicalRecord = 'السجل الطبي';

  // المواعيد
  static const String myAppointments = 'مواعيدي';
  static const String clinicInfo = 'معلومات العيادة';
  static const String appointmentDate = 'تاريخ الموعد';
  static const String appointmentTime = 'وقت الموعد';
  static const String appointmentStatus = 'حالة الموعد';
  static const String appointmentNotes = 'ملاحظات الموعد';
  static const String available = 'متاح';
  static const String booked = 'محجوز';
  static const String completed = 'مكتمل';
  static const String cancelled = 'ملغي';
  static const String selectDate = 'اختر التاريخ';
  static const String selectTime = 'اختر الوقت';
  static const String bookingConfirmed = 'تم تأكيد الحجز';
  static const String bookingCancelled = 'تم إلغاء الحجز';

  // المنتجات
  static const String productName = 'اسم المنتج';
  static const String productDescription = 'وصف المنتج';
  static const String productPrice = 'السعر';
  static const String productCategory = 'الفئة';
  static const String addToCart = 'إضافة للسلة';
  static const String outOfStock = 'غير متوفر';
  static const String inStock = 'متوفر';
  static const String discount = 'خصم';

  // الملف الشخصي
  static const String personalInfo = 'المعلومات الشخصية';
  static const String medicalInfo = 'المعلومات الطبية';
  static const String age = 'العمر';
  static const String gender = 'الجنس';
  static const String height = 'الطول';
  static const String weight = 'الوزن';
  static const String birthDate = 'تاريخ الميلاد';
  static const String male = 'ذكر';
  static const String female = 'أنثى';
  static const String medicalConditions = 'الحالات الطبية';
  static const String allergies = 'الحساسية';
  static const String medications = 'الأدوية';
  static const String supplements = 'المكملات الغذائية';
  static const String physicalActivity = 'النشاط البدني';
  static const String notes = 'ملاحظات';

  // السجل الطبي - التبويبات الجديدة
  static const String diagnosis = 'التشخيص والتقرير';
  static const String assignments = 'الواجبات';
  static const String purchases = 'المشتريات';
  static const String weeklyResults = 'النتائج الأسبوعية';
  static const String labTests = 'الفحوصات المخبرية';
  static const String bodyFat = 'نسبة الدهون';
  static const String visceralFat = 'الدهون الحشوية';
  static const String waterPercentage = 'نسبة الماء';
  static const String muscleMass = 'الكتلة العضلية';
  static const String recordedDate = 'تاريخ التسجيل';

  // التذكيرات
  static const String reminders = 'التذكيرات';
  static const String appointmentReminder = 'تذكير بالموعد';
  static const String mealReminder = 'تذكير بالوجبة';
  static const String waterReminder = 'تذكير بشرب الماء';
  static const String exerciseReminder = 'تذكير بالتمارين';
  static const String medicationReminder = 'تذكير بالدواء';

  // رسائل الخطأ
  static const String emailRequired = 'البريد الإلكتروني مطلوب';
  static const String emailInvalid = 'البريد الإلكتروني غير صحيح';
  static const String passwordRequired = 'كلمة المرور مطلوبة';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';
  static const String nameRequired = 'الاسم مطلوب';
  static const String phoneRequired = 'رقم الهاتف مطلوب';
  static const String phoneInvalid = 'رقم الهاتف غير صحيح';
  static const String networkError = 'خطأ في الاتصال بالشبكة';
  static const String serverError = 'خطأ في الخادم';
  static const String unknownError = 'خطأ غير معروف';
  static const String noInternetConnection = 'لا يوجد اتصال بالإنترنت';

  // رسائل النجاح
  static const String loginSuccess = 'تم تسجيل الدخول بنجاح';
  static const String profileUpdated = 'تم تحديث الملف الشخصي';
  static const String appointmentBooked = 'تم حجز الموعد بنجاح';
  static const String reminderSet = 'تم تعيين التذكير';

  // رسائل الحالة الفارغة
  static const String noAppointmentsMessage = 'لا توجد مواعيد';
  static const String noProductsMessage = 'لا توجد منتجات';

  // حقول إضافية للتسجيل
  static const String approximateWeight = 'الوزن التقريبي';
  static const String approximateHeight = 'الطول التقريبي';

  // أخرى
  static const String comingSoon = 'قريباً';
  static const String underDevelopment = 'تحت التطوير';
  static const String version = 'الإصدار';
  static const String aboutApp = 'حول التطبيق';
  static const String contactUs = 'اتصل بنا';
  static const String privacyPolicy = 'سياسة الخصوصية';
  static const String termsOfService = 'شروط الخدمة';
  static const String developedBy = 'Developed by';
  static const String khwasstech = 'khwasstech.com';

  // إضافات جديدة للتطبيق المحدث
  static const String foodAnalysis = 'تحليل الطعام';
  static const String articles = 'المقالات';
  static const String premiumUser = 'عضو مميز';
  static const String passwordsNotMatch = 'كلمات المرور غير متطابقة';
  static const String confirmPassword = 'تأكيد كلمة المرور';
  static const String alreadyHaveAccount = 'لديك حساب بالفعل؟';
  static const String bookAppointment = 'حجز موعد';
  static const String takePicture = 'التقاط صورة';
}
