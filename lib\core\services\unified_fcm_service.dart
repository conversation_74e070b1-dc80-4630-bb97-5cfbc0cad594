import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';

/// معالج الرسائل في الخلفية (يجب أن يكون في المستوى الأعلى)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    // تهيئة Firebase إذا لم يكن مهيأ
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp();
    }
    
    AppLogger.info(
      '🔔 Background message received',
      category: LogCategory.notification,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'title': message.notification?.title ?? 'No title',
        'body': message.notification?.body ?? 'No body',
        'data': message.data.toString(),
      },
    );

    // عرض الإشعار المحلي
    await UnifiedFCMService._showLocalNotification(message);
  } catch (e) {
    AppLogger.error('Error in background message handler', error: e);
  }
}

/// خدمة FCM موحدة ومحسنة للعمل في جميع البيئات
class UnifiedFCMService {
  static final UnifiedFCMService _instance = UnifiedFCMService._internal();
  factory UnifiedFCMService() => _instance;
  UnifiedFCMService._internal();

  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  static final SupabaseClient _supabase = Supabase.instance.client;
  
  bool _isInitialized = false;
  String? _fcmToken;

  /// تهيئة خدمة FCM
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      AppLogger.info('🚀 Initializing Unified FCM Service', category: LogCategory.notification);

      // 1. طلب الأذونات أولاً
      await _requestPermissions();

      // 2. تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // 3. إعداد معالجات الرسائل
      await _setupMessageHandlers();

      // 4. الحصول على FCM Token
      await _getFCMToken();

      // 5. حفظ التوكن في قاعدة البيانات
      await _saveTokenToDatabase();

      _isInitialized = true;
      AppLogger.info('✅ Unified FCM Service initialized successfully', category: LogCategory.notification);
      
      return true;
    } catch (e) {
      AppLogger.error('❌ Failed to initialize Unified FCM Service', category: LogCategory.notification, error: e);
      return false;
    }
  }

  /// طلب الأذونات
  Future<void> _requestPermissions() async {
    try {
      // طلب أذونات Firebase
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      AppLogger.info(
        'FCM Permissions status: ${settings.authorizationStatus}',
        category: LogCategory.notification,
      );

      // طلب أذونات الإشعارات المحلية للأندرويد
      if (Platform.isAndroid) {
        await _localNotifications
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();
      }
    } catch (e) {
      AppLogger.error('Failed to request permissions', category: LogCategory.notification, error: e);
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    try {
      // إعدادات Android
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      
      // إعدادات iOS
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // إنشاء قناة الإشعارات للأندرويد
      if (Platform.isAndroid) {
        const channel = AndroidNotificationChannel(
          'iihc_notifications',
          'IIHC Notifications',
          description: 'إشعارات عيادة السمع والنطق',
          importance: Importance.high,
          playSound: true,
        );

        await _localNotifications
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
            ?.createNotificationChannel(channel);
      }

      AppLogger.info('Local notifications initialized', category: LogCategory.notification);
    } catch (e) {
      AppLogger.error('Failed to initialize local notifications', category: LogCategory.notification, error: e);
    }
  }

  /// إعداد معالجات الرسائل
  Future<void> _setupMessageHandlers() async {
    try {
      // معالج الرسائل في المقدمة
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // معالج النقر على الإشعار
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      // معالج الرسائل في الخلفية
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // التحقق من الرسائل عند فتح التطبيق
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleNotificationTap(initialMessage);
      }

      AppLogger.info('Message handlers setup complete', category: LogCategory.notification);
    } catch (e) {
      AppLogger.error('Failed to setup message handlers', category: LogCategory.notification, error: e);
    }
  }

  /// معالج الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      AppLogger.info(
        '📱 Foreground message received',
        category: LogCategory.notification,
        data: {
          'title': message.notification?.title ?? 'No title',
          'body': message.notification?.body ?? 'No body',
        },
      );

      // عرض الإشعار المحلي
      await _showLocalNotification(message);
    } catch (e) {
      AppLogger.error('Error handling foreground message', category: LogCategory.notification, error: e);
    }
  }

  /// معالج النقر على الإشعار
  void _handleNotificationTap(RemoteMessage message) {
    try {
      AppLogger.info(
        '👆 Notification tapped',
        category: LogCategory.notification,
        data: message.data,
      );

      // يمكن إضافة منطق التنقل هنا
      // مثل فتح صفحة معينة حسب نوع الإشعار
    } catch (e) {
      AppLogger.error('Error handling notification tap', category: LogCategory.notification, error: e);
    }
  }

  /// معالج النقر على الإشعار المحلي
  void _onNotificationTapped(NotificationResponse response) {
    try {
      AppLogger.info(
        '👆 Local notification tapped',
        category: LogCategory.notification,
        data: {'payload': response.payload},
      );

      // يمكن إضافة منطق التنقل هنا
    } catch (e) {
      AppLogger.error('Error handling local notification tap', category: LogCategory.notification, error: e);
    }
  }

  /// عرض الإشعار المحلي
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'iihc_notifications',
        'IIHC Notifications',
        channelDescription: 'إشعارات عيادة السمع والنطق',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        playSound: true,
        enableVibration: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        message.hashCode,
        message.notification?.title ?? 'إشعار جديد',
        message.notification?.body ?? 'لديك إشعار جديد',
        details,
        payload: message.data.toString(),
      );
    } catch (e) {
      AppLogger.error('Error showing local notification', error: e);
    }
  }

  /// الحصول على FCM Token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      
      AppLogger.info(
        'FCM Token obtained',
        category: LogCategory.notification,
        data: {'token': _fcmToken?.substring(0, 20) ?? 'null'},
      );

      // مراقبة تغيير Token
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        AppLogger.info('FCM Token refreshed', category: LogCategory.notification);
        _saveTokenToDatabase();
      });
    } catch (e) {
      AppLogger.error('Failed to get FCM token', category: LogCategory.notification, error: e);
    }
  }

  /// حفظ التوكن في قاعدة البيانات
  Future<void> _saveTokenToDatabase() async {
    try {
      if (_fcmToken == null || _supabase.auth.currentUser == null) return;

      final userId = _supabase.auth.currentUser!.id;
      
      // محاولة تحديث التوكن الموجود أو إدراج جديد
      await _supabase.from('user_fcm_tokens').upsert({
        'user_id': userId,
        'fcm_token': _fcmToken,
        'device_type': Platform.isAndroid ? 'android' : 'ios',
        'is_active': true,
        'updated_at': DateTime.now().toIso8601String(),
      });

      AppLogger.info('FCM token saved to database', category: LogCategory.notification);
    } catch (e) {
      AppLogger.error('Failed to save FCM token to database', category: LogCategory.notification, error: e);
    }
  }

  /// الحصول على FCM Token الحالي
  String? get fcmToken => _fcmToken;

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;
}
