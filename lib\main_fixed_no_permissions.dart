import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'core/services/notification_initialization_service_fixed.dart';
import 'core/utils/app_logger.dart';

void main() async {
  // تأكد من تهيئة Flutter
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة التسجيل
  AppLogger.info('🚀 Starting IIHC User App');

  try {
    // تهيئة نظام الإشعارات أولاً
    await _initializeNotifications();

    // تهيئة Supabase
    await _initializeSupabase();

    // إعدادات النظام
    await _setupSystemSettings();

    // تشغيل التطبيق
    runApp(const MyApp());

    AppLogger.info('✅ App started successfully');
  } catch (e) {
    AppLogger.error('❌ Failed to start app', error: e);
    
    // تشغيل التطبيق حتى لو فشلت بعض الخدمات
    runApp(const MyApp());
  }
}

/// تهيئة نظام الإشعارات
Future<void> _initializeNotifications() async {
  try {
    AppLogger.info('🔔 Initializing notification system...');
    
    final notificationService = NotificationInitializationServiceFixed();
    final success = await notificationService.initializeNotifications();
    
    if (success) {
      AppLogger.info('✅ Notification system initialized successfully');
    } else {
      AppLogger.warning('⚠️ Notification system initialization failed');
    }
  } catch (e) {
    AppLogger.error('❌ Critical error in notification initialization', error: e);
    // لا نرمي الخطأ هنا لأننا نريد التطبيق أن يعمل حتى لو فشلت الإشعارات
  }
}

/// تهيئة Supabase
Future<void> _initializeSupabase() async {
  try {
    AppLogger.info('🗄️ Initializing Supabase...');
    
    await Supabase.initialize(
      url: 'https://xqvdkdjnrcytswvfrkog.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhxdmRrZGpucmN5dHN3dmZya29nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMTI5MzAsImV4cCI6MjA2ODc4ODkzMH0.zaHSYMMK1QIRwCckgZXhT287rdW2IQUbY5Ag4U7PiRg',
      debug: false, // تعطيل Debug في Release
    );
    
    AppLogger.info('✅ Supabase initialized successfully');
  } catch (e) {
    AppLogger.error('❌ Failed to initialize Supabase', error: e);
    rethrow; // هذا مهم للتطبيق
  }
}

/// إعدادات النظام
Future<void> _setupSystemSettings() async {
  try {
    // إعداد اتجاه الشاشة
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    // إعداد شريط الحالة
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    AppLogger.info('✅ System settings configured');
  } catch (e) {
    AppLogger.warning('⚠️ Failed to setup system settings', error: e);
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: 'IIHC User',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primarySwatch: Colors.teal,
            fontFamily: 'Cairo',
            visualDensity: VisualDensity.adaptivePlatformDensity,
          ),
          home: const SplashScreen(),
        );
      },
    );
  }
}

/// شاشة البداية مع فحص النظام
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkSystemAndNavigate();
  }

  Future<void> _checkSystemAndNavigate() async {
    try {
      // انتظار قصير لعرض الشاشة
      await Future.delayed(const Duration(seconds: 2));

      // فحص حالة نظام الإشعارات
      final notificationService = NotificationInitializationServiceFixed();
      final status = await notificationService.getSystemStatus();
      
      AppLogger.info(
        'System status check',
        data: status,
      );

      // التنقل للشاشة الرئيسية
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const MainScreen(),
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Error in splash screen', error: e);
      
      // التنقل حتى لو حدث خطأ
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const MainScreen(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF04938c),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // شعار التطبيق
            Container(
              width: 120.w,
              height: 120.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Icon(
                Icons.local_hospital,
                size: 60.sp,
                color: const Color(0xFF04938c),
              ),
            ),
            
            SizedBox(height: 30.h),
            
            // اسم التطبيق
            Text(
              'IIHC User',
              style: TextStyle(
                fontSize: 28.sp,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            
            SizedBox(height: 10.h),
            
            Text(
              'تطبيق المرضى',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
            
            SizedBox(height: 50.h),
            
            // مؤشر التحميل
            SizedBox(
              width: 30.w,
              height: 30.h,
              child: const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 3,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// الشاشة الرئيسية (مؤقتة)
class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('IIHC User'),
        backgroundColor: const Color(0xFF04938c),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 80.sp,
              color: const Color(0xFF04938c),
            ),
            SizedBox(height: 20.h),
            Text(
              'التطبيق جاهز!',
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF04938c),
              ),
            ),
            SizedBox(height: 10.h),
            Text(
              'نظام الإشعارات يعمل بكفاءة',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 30.h),
            ElevatedButton(
              onPressed: () async {
                // اختبار نظام الإشعارات
                final service = NotificationInitializationServiceFixed();
                final status = await service.getSystemStatus();
                
                if (context.mounted) {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('حالة النظام'),
                      content: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text('مُهيأ: ${status['isInitialized']}'),
                            Text('FCM مُهيأ: ${status['fcmInitialized']}'),
                            Text('يوجد Token: ${status['hasToken']}'),
                            Text('طول Token: ${status['tokenLength']}'),
                            Text('المنصة: ${status['platform']}'),
                            Text('Release: ${status['isRelease']}'),
                            Text('الوقت: ${status['timestamp']}'),
                          ],
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('موافق'),
                        ),
                        TextButton(
                          onPressed: () async {
                            Navigator.pop(context);
                            // اختبار الأذونات
                            final hasPermissions = await service.checkNotificationPermissions();
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('أذونات الإشعارات: ${hasPermissions ? 'مُفعلة' : 'غير مُفعلة'}'),
                                ),
                              );
                            }
                          },
                          child: const Text('فحص الأذونات'),
                        ),
                      ],
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF04938c),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 15.h),
              ),
              child: const Text('فحص النظام'),
            ),
            SizedBox(height: 20.h),
            ElevatedButton(
              onPressed: () async {
                // الحصول على FCM Token
                final service = NotificationInitializationServiceFixed();
                final token = await service.getFCMToken();
                
                if (context.mounted) {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('FCM Token'),
                      content: SelectableText(
                        token ?? 'لا يوجد Token',
                        style: const TextStyle(fontSize: 12),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('موافق'),
                        ),
                      ],
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 15.h),
              ),
              child: const Text('عرض FCM Token'),
            ),
          ],
        ),
      ),
    );
  }
}