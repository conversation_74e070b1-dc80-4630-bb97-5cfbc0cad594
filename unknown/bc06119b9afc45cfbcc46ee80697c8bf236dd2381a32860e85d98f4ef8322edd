import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/app_logger.dart';
import '../../../data/repositories/notification_repository.dart';
import '../notifications/notifications_page.dart';
import 'tabs/my_appointments_tab.dart';
import 'tabs/clinic_info_tab.dart';

/// صفحة المواعيد مع أيقونة الإشعارات
class AppointmentsPage extends StatefulWidget {
  final String authId; // معرف المصادقة

  const AppointmentsPage({super.key, required this.authId});

  @override
  State<AppointmentsPage> createState() => _AppointmentsPageState();
}

class _AppointmentsPageState extends State<AppointmentsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final NotificationRepository _notificationRepository =
      NotificationRepository();

  String? _patientId; // معرف المريض الحقيقي
  int _unreadNotificationsCount = 0;

  @override
  void initState() {
    super.initState();
    AppLogger.info(
      'AppointmentsPage initializing with authId: ${widget.authId}',
      category: LogCategory.ui,
    );
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
    _loadPatientId(); // تحميل معرف المريض الحقيقي
    _loadUnreadNotificationsCount(); // تحميل عدد الإشعارات غير المقروءة
  }

  /// تحميل معرف المريض من جدول patients باستخدام id
  Future<void> _loadPatientId() async {
    try {
      final response =
          await Supabase.instance.client
              .from('patients')
              .select('id')
              .eq('id', widget.authId)
              .single();

      if (mounted) {
        setState(() {
          _patientId = response['id'] as String;
        });
        AppLogger.info(
          'Patient ID loaded: $_patientId for patient_id: ${widget.authId}',
          category: LogCategory.ui,
        );
      }
    } catch (e) {
      AppLogger.error(
        'Failed to load patient ID for patient_id: ${widget.authId}',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  /// تحميل عدد الإشعارات غير المقروءة
  Future<void> _loadUnreadNotificationsCount() async {
    try {
      final count = await _notificationRepository.getUnreadNotificationsCount(
        userId: widget.authId,
      );

      if (mounted) {
        setState(() {
          _unreadNotificationsCount = count;
        });
      }
    } catch (e) {
      AppLogger.error(
        'Failed to load unread notifications count',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  /// الانتقال لصفحة الإشعارات
  void _navigateToNotifications() async {
    await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const NotificationsPage()));

    // تحديث عدد الإشعارات غير المقروءة عند العودة
    _loadUnreadNotificationsCount();
  }

  void _onTabChanged() {
    final tabNames = ['مواعيدي', 'معلومات العيادة'];
    AppLogger.info(
      'AppointmentsPage tab changed to: ${tabNames[_tabController.index]}',
      category: LogCategory.ui,
    );
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.white,
        elevation: 0,
        title: Text(
          AppStrings.appointments,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: AppColors.textPrimary,
            size: 20.sp,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          // أيقونة الإشعارات مع العداد
          Stack(
            children: [
              IconButton(
                icon: Icon(
                  Icons.notifications_outlined,
                  color: AppColors.textPrimary,
                  size: 24.sp,
                ),
                onPressed: _navigateToNotifications,
                tooltip: 'الإشعارات',
              ),
              if (_unreadNotificationsCount > 0)
                Positioned(
                  right: 8.w,
                  top: 8.h,
                  child: Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: AppColors.error,
                      borderRadius: BorderRadius.circular(10.r),
                    ),
                    constraints: BoxConstraints(
                      minWidth: 16.w,
                      minHeight: 16.h,
                    ),
                    child: Text(
                      _unreadNotificationsCount > 99
                          ? '99+'
                          : _unreadNotificationsCount.toString(),
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(width: 8.w),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textLight,
          indicatorColor: AppColors.primary,
          labelStyle: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.calendar_today), text: 'مواعيدي'),
            Tab(icon: Icon(Icons.info_outline), text: 'معلومات العيادة'),
          ],
        ),
      ),
      body:
          _patientId != null
              ? TabBarView(
                controller: _tabController,
                children: [
                  MyAppointmentsTab(patientId: _patientId!),
                  const ClinicInfoTab(),
                ],
              )
              : const Center(child: CircularProgressIndicator()),
    );
  }
}