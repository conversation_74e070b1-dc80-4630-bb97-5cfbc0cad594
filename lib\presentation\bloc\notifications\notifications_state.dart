part of 'notifications_bloc.dart';

/// حالات الإشعارات
abstract class NotificationsState extends Equatable {
  const NotificationsState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class NotificationsInitial extends NotificationsState {}

/// حالة التحميل
class NotificationsLoading extends NotificationsState {}

/// حالة تهيئة الإشعارات بنجاح
class NotificationsInitialized extends NotificationsState {
  final String? fcmToken;

  const NotificationsInitialized({this.fcmToken});

  @override
  List<Object?> get props => [fcmToken];
}

/// حالة نتيجة طلب الأذونات
class NotificationPermissionsResult extends NotificationsState {
  final bool granted;

  const NotificationPermissionsResult({required this.granted});

  @override
  List<Object?> get props => [granted];
}

/// حالة عرض إشعار بنجاح
class NotificationShown extends NotificationsState {
  final int id;

  const NotificationShown({required this.id});

  @override
  List<Object?> get props => [id];
}

/// حالة جدولة إشعار بنجاح
class NotificationScheduled extends NotificationsState {
  final int id;
  final DateTime scheduledDate;

  const NotificationScheduled({
    required this.id,
    required this.scheduledDate,
  });

  @override
  List<Object?> get props => [id, scheduledDate];
}

/// حالة إلغاء إشعار بنجاح
class NotificationCancelled extends NotificationsState {
  final int id;

  const NotificationCancelled({required this.id});

  @override
  List<Object?> get props => [id];
}

/// حالة إلغاء جميع الإشعارات بنجاح
class AllNotificationsCancelled extends NotificationsState {}

/// حالة تحديث إعدادات الإشعارات بنجاح
class NotificationSettingsUpdated extends NotificationsState {
  final bool enableAppointmentReminders;
  final bool enableHomeworkNotifications;
  final bool enableExaminationResults;

  const NotificationSettingsUpdated({
    required this.enableAppointmentReminders,
    required this.enableHomeworkNotifications,
    required this.enableExaminationResults,
  });

  @override
  List<Object?> get props => [
    enableAppointmentReminders,
    enableHomeworkNotifications,
    enableExaminationResults,
  ];
}

/// حالة الاشتراك في موضوع بنجاح
class TopicSubscribed extends NotificationsState {
  final String topic;

  const TopicSubscribed({required this.topic});

  @override
  List<Object?> get props => [topic];
}

/// حالة إلغاء الاشتراك من موضوع بنجاح
class TopicUnsubscribed extends NotificationsState {
  final String topic;

  const TopicUnsubscribed({required this.topic});

  @override
  List<Object?> get props => [topic];
}

/// حالة الخطأ
class NotificationsError extends NotificationsState {
  final String message;

  const NotificationsError({required this.message});

  @override
  List<Object?> get props => [message];
}