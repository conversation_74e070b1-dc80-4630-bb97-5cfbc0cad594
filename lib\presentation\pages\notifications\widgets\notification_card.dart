import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../data/models/notification_model.dart';

/// بطاقة الإشعار
class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: notification.isRead 
              ? AppColors.border 
              : AppColors.primary.withValues(alpha: 0.3),
          width: notification.isRead ? 1 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12.r),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس البطاقة
                _buildHeader(),
                
                SizedBox(height: 8.h),
                
                // محتوى الإشعار
                _buildContent(),
                
                SizedBox(height: 8.h),
                
                // تذييل البطاقة
                _buildFooter(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // أيقونة الإشعار
        Container(
          width: 40.w,
          height: 40.h,
          decoration: BoxDecoration(
            color: _getNotificationColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            _getNotificationIcon(),
            color: _getNotificationColor(),
            size: 20.sp,
          ),
        ),
        
        SizedBox(width: 12.w),
        
        // عنوان الإشعار ونوعه
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                notification.title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 2.h),
              Text(
                notification.typeDisplayName,
                style: TextStyle(
                  fontSize: 11.sp,
                  color: _getNotificationColor(),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        
        // مؤشر عدم القراءة وقائمة الخيارات
        Column(
          children: [
            if (!notification.isRead)
              Container(
                width: 8.w,
                height: 8.h,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
              ),
            
            SizedBox(height: 4.h),
            
            // قائمة الخيارات
            PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                color: AppColors.textSecondary,
                size: 16.sp,
              ),
              onSelected: (value) {
                switch (value) {
                  case 'delete':
                    onDelete?.call();
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: AppColors.error, size: 16.sp),
                      SizedBox(width: 8.w),
                      Text('حذف', style: TextStyle(fontSize: 12.sp)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (notification.bodyText.isEmpty) {
      return const SizedBox.shrink();
    }

    return Text(
      notification.bodyText,
      style: TextStyle(
        fontSize: 13.sp,
        color: AppColors.textSecondary,
        height: 1.4,
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildFooter() {
    return Row(
      children: [
        // وقت الإشعار
        Icon(
          Icons.access_time,
          color: AppColors.textLight,
          size: 12.sp,
        ),
        SizedBox(width: 4.w),
        Text(
          notification.timeAgo,
          style: TextStyle(
            fontSize: 11.sp,
            color: AppColors.textLight,
          ),
        ),
        
        const Spacer(),
        
        // حالة القراءة
        if (notification.isRead)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              'مقروء',
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.success,
                fontWeight: FontWeight.w500,
              ),
            ),
          )
        else
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              'جديد',
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  Color _getNotificationColor() {
    switch (notification.type.toLowerCase()) {
      case 'appointment_confirmed':
      case 'appointment_reminder':
        return AppColors.success;
      case 'appointment_cancelled':
        return AppColors.error;
      case 'appointment_rescheduled':
        return AppColors.warning;
      case 'payment_received':
        return AppColors.primary;
      case 'payment_reminder':
        return AppColors.error;
      case 'general':
      case 'announcement':
        return AppColors.info;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getNotificationIcon() {
    switch (notification.type.toLowerCase()) {
      case 'appointment_confirmed':
        return Icons.event_available;
      case 'appointment_cancelled':
        return Icons.event_busy;
      case 'appointment_rescheduled':
        return Icons.event_repeat;
      case 'appointment_reminder':
        return Icons.schedule;
      case 'payment_received':
        return Icons.payment;
      case 'payment_reminder':
        return Icons.payment_outlined;
      case 'general':
        return Icons.info;
      case 'announcement':
        return Icons.campaign;
      default:
        return Icons.notifications;
    }
  }
}