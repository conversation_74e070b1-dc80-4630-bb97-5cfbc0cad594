class TimeSlotModel {
  final String id;
  final String startTime;
  final String endTime;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int dayOfWeek;
  final int durationMinutes;
  final int maxPatients;
  final String employeeId;

  TimeSlotModel({
    required this.id,
    required this.startTime,
    required this.endTime,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.dayOfWeek = 1,
    this.durationMinutes = 30,
    this.maxPatients = 1,
    required this.employeeId,
  });

  factory TimeSlotModel.fromJson(Map<String, dynamic> json) {
    return TimeSlotModel(
      id: json['id'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      dayOfWeek: json['day_of_week'] ?? 1,
      durationMinutes: json['duration_minutes'] ?? 30,
      maxPatients: json['max_patients'] ?? 1,
      employeeId: json['employee_id'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'start_time': startTime,
      'end_time': endTime,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'day_of_week': dayOfWeek,
      'duration_minutes': durationMinutes,
      'max_patients': maxPatients,
      'employee_id': employeeId,
    };
  }

  TimeSlotModel copyWith({
    String? id,
    String? startTime,
    String? endTime,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? dayOfWeek,
    int? durationMinutes,
    int? maxPatients,
    String? employeeId,
  }) {
    return TimeSlotModel(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      maxPatients: maxPatients ?? this.maxPatients,
      employeeId: employeeId ?? this.employeeId,
    );
  }

  // Helper methods
  bool get hasEmployee => employeeId.isNotEmpty;
  
  // Time formatting helpers
  String get formattedStartTime {
    try {
      final parts = startTime.split(':');
      if (parts.length >= 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        final period = hour >= 12 ? 'م' : 'ص';
        final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
        return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
      }
    } catch (e) {
      // Fallback to original format
    }
    return startTime;
  }
  
  String get formattedEndTime {
    try {
      final parts = endTime.split(':');
      if (parts.length >= 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        final period = hour >= 12 ? 'م' : 'ص';
        final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
        return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
      }
    } catch (e) {
      // Fallback to original format
    }
    return endTime;
  }
  
  String get timeRange => '$formattedStartTime - $formattedEndTime';
  String get displayTimeRange => timeRange;
  String get displayTimeRange12Hour => timeRange;
  
  // Static method to generate time slots
  static List<TimeSlotModel> generateTimeSlots({
    required String employeeId,
    required int dayOfWeek,
    String startTime = '09:00',
    String endTime = '17:00',
    int slotDuration = 30,
    int breakDuration = 0,
    List<String> breakTimes = const [],
  }) {
    final slots = <TimeSlotModel>[];
    
    try {
      final startParts = startTime.split(':');
      final endParts = endTime.split(':');
      
      if (startParts.length < 2 || endParts.length < 2) {
        return slots;
      }
      
      final startHour = int.parse(startParts[0]);
      final startMinute = int.parse(startParts[1]);
      final endHour = int.parse(endParts[0]);
      final endMinute = int.parse(endParts[1]);
      
      final startDateTime = DateTime(2024, 1, 1, startHour, startMinute);
      final endDateTime = DateTime(2024, 1, 1, endHour, endMinute);
      
      DateTime currentTime = startDateTime;
      int slotIndex = 0;
      
      while (currentTime.add(Duration(minutes: slotDuration)).isBefore(endDateTime) ||
             currentTime.add(Duration(minutes: slotDuration)).isAtSameMomentAs(endDateTime)) {
        
        final slotStart = currentTime;
        final slotEnd = currentTime.add(Duration(minutes: slotDuration));
        
        final startTimeStr = '${slotStart.hour.toString().padLeft(2, '0')}:${slotStart.minute.toString().padLeft(2, '0')}';
        final endTimeStr = '${slotEnd.hour.toString().padLeft(2, '0')}:${slotEnd.minute.toString().padLeft(2, '0')}';
        
        // Check if this time is in break times
        if (!breakTimes.contains(startTimeStr)) {
          slots.add(TimeSlotModel(
            id: 'slot_${employeeId}_${dayOfWeek}_${slotIndex}',
            startTime: startTimeStr,
            endTime: endTimeStr,
            dayOfWeek: dayOfWeek,
            durationMinutes: slotDuration,
            employeeId: employeeId,
            isActive: true,
            maxPatients: 1,
          ));
        }
        
        currentTime = currentTime.add(Duration(minutes: slotDuration + breakDuration));
        slotIndex++;
      }
    } catch (e) {
      // Handle parsing errors
    }
    
    return slots;
  }
  
  // Day of week helpers
  String get dayOfWeekText {
    const days = [
      '', // 0 is not used
      'الاثنين',    // 1
      'الثلاثاء',   // 2
      'الأربعاء',   // 3
      'الخميس',    // 4
      'الجمعة',    // 5
      'السبت',     // 6
      'الأحد',     // 7
    ];
    
    if (dayOfWeek >= 1 && dayOfWeek <= 7) {
      return days[dayOfWeek];
    }
    return 'غير محدد';
  }
  
  String get dayOfWeekShort {
    const days = [
      '', // 0 is not used
      'اثنين',    // 1
      'ثلاثاء',   // 2
      'أربعاء',   // 3
      'خميس',    // 4
      'جمعة',    // 5
      'سبت',     // 6
      'أحد',     // 7
    ];
    
    if (dayOfWeek >= 1 && dayOfWeek <= 7) {
      return days[dayOfWeek];
    }
    return 'غير محدد';
  }
  
  bool get isWeekend => dayOfWeek == 5 || dayOfWeek == 6; // Friday or Saturday
  bool get isWeekday => !isWeekend;
  
  // Duration helpers
  String get formattedDuration {
    if (durationMinutes >= 60) {
      final hours = durationMinutes ~/ 60;
      final minutes = durationMinutes % 60;
      if (minutes == 0) {
        return '$hours ${hours == 1 ? 'ساعة' : 'ساعات'}';
      } else {
        return '$hours ${hours == 1 ? 'ساعة' : 'ساعات'} و $minutes ${minutes == 1 ? 'دقيقة' : 'دقائق'}';
      }
    } else {
      return '$durationMinutes ${durationMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    }
  }
  
  // Capacity helpers
  String get capacityText {
    if (maxPatients == 1) {
      return 'مريض واحد';
    } else {
      return '$maxPatients ${maxPatients <= 10 ? 'مرضى' : 'مريضاً'}';
    }
  }
  
  bool get isMultiPatientSlot => maxPatients > 1;
  
  // Status helpers
  String get statusText => isActive ? 'نشط' : 'غير نشط';
  
  // Time calculations
  DateTime? get startDateTime {
    try {
      final parts = startTime.split(':');
      if (parts.length >= 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        final now = DateTime.now();
        return DateTime(now.year, now.month, now.day, hour, minute);
      }
    } catch (e) {
      // Handle parsing error
    }
    return null;
  }
  
  DateTime? get endDateTime {
    try {
      final parts = endTime.split(':');
      if (parts.length >= 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        final now = DateTime.now();
        return DateTime(now.year, now.month, now.day, hour, minute);
      }
    } catch (e) {
      // Handle parsing error
    }
    return null;
  }
  
  int get actualDurationMinutes {
    final start = startDateTime;
    final end = endDateTime;
    if (start != null && end != null) {
      return end.difference(start).inMinutes;
    }
    return durationMinutes;
  }
  
  // Time period helpers
  bool get isMorningSlot {
    final start = startDateTime;
    if (start != null) {
      return start.hour < 12;
    }
    return false;
  }
  
  bool get isAfternoonSlot {
    final start = startDateTime;
    if (start != null) {
      return start.hour >= 12 && start.hour < 18;
    }
    return false;
  }
  
  bool get isEveningSlot {
    final start = startDateTime;
    if (start != null) {
      return start.hour >= 18;
    }
    return false;
  }
  
  String get timePeriod {
    if (isMorningSlot) return 'صباحي';
    if (isAfternoonSlot) return 'بعد الظهر';
    if (isEveningSlot) return 'مسائي';
    return 'غير محدد';
  }
  
  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }
  
  // Validation
  bool get isValidTimeSlot {
    final start = startDateTime;
    final end = endDateTime;
    return start != null && end != null && end.isAfter(start);
  }
  
  @override
  String toString() {
    return 'TimeSlotModel(id: $id, day: $dayOfWeekText, time: $timeRange, duration: $formattedDuration)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeSlotModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
