import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/app_logger.dart';

/// خدمة FCM محسنة للعمل في Release وجميع إصدارات Android/iOS
class FCMServiceReleaseFix {
  static final FCMServiceReleaseFix _instance = FCMServiceReleaseFix._internal();
  factory FCMServiceReleaseFix() => _instance;
  FCMServiceReleaseFix._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  bool _isInitialized = false;
  String? _fcmToken;
  
  // Channel للإشعارات المحلية
  static const AndroidNotificationChannel _channel = AndroidNotificationChannel(
    'high_importance_channel',
    'إشعارات مهمة',
    description: 'قناة للإشعارات المهمة',
    importance: Importance.high,
    playSound: true,
    enableVibration: true,
    enableLights: true,
  );

  /// تهيئة خدمة FCM
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      AppLogger.info('🚀 Initializing FCM Service for Release', category: LogCategory.fcm);

      // تهيئة Firebase
      await _initializeFirebase();

      // طلب الأذونات
      await _requestPermissions();

      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // إعداد معالجات الرسائل
      await _setupMessageHandlers();

      // الحصول على FCM Token
      await _getFCMToken();

      _isInitialized = true;
      AppLogger.info('✅ FCM Service initialized successfully', category: LogCategory.fcm);
      
      return true;
    } catch (e) {
      AppLogger.error('❌ Failed to initialize FCM Service', category: LogCategory.fcm, error: e);
      return false;
    }
  }

  /// تهيئة Firebase
  Future<void> _initializeFirebase() async {
    try {
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
        AppLogger.info('Firebase initialized', category: LogCategory.fcm);
      }
    } catch (e) {
      AppLogger.error('Failed to initialize Firebase', category: LogCategory.fcm, error: e);
      rethrow;
    }
  }

  /// طلب الأذونات للإشعارات
  Future<void> _requestPermissions() async {
    try {
      // أذونات Firebase Messaging
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      AppLogger.info(
        'FCM Permission status: ${settings.authorizationStatus}',
        category: LogCategory.fcm,
      );

      // أذونات إضافية للـ Android
      if (Platform.isAndroid) {
        await _requestAndroidPermissions();
      }

      // أذونات iOS
      if (Platform.isIOS) {
        await _requestIOSPermissions();
      }

    } catch (e) {
      AppLogger.error('Failed to request permissions', category: LogCategory.fcm, error: e);
    }
  }

  /// طلب أذونات Android
  Future<void> _requestAndroidPermissions() async {
    try {
      // Android 13+ notification permission
      if (await _isAndroid13OrHigher()) {
        final status = await Permission.notification.request();
        AppLogger.info('Android 13+ notification permission: $status', category: LogCategory.fcm);
      }

      // أذونات إضافية
      await Permission.ignoreBatteryOptimizations.request();
      
    } catch (e) {
      AppLogger.warning('Failed to request Android permissions', category: LogCategory.fcm, error: e);
    }
  }

  /// طلب أذونات iOS
  Future<void> _requestIOSPermissions() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );
      
      AppLogger.info('iOS notification permission: ${settings.authorizationStatus}', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.warning('Failed to request iOS permissions', category: LogCategory.fcm, error: e);
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    try {
      // إعدادات Android
      const androidSettings = AndroidInitializationSettings('@mipmap/launcher_icon');
      
      // إعدادات iOS
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // إنشاء قناة الإشعارات للـ Android
      if (Platform.isAndroid) {
        await _localNotifications
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
            ?.createNotificationChannel(_channel);
      }

      AppLogger.info('Local notifications initialized', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.error('Failed to initialize local notifications', category: LogCategory.fcm, error: e);
    }
  }

  /// إعداد معالجات الرسائل
  Future<void> _setupMessageHandlers() async {
    try {
      // معالج الرسائل في المقدمة
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // معالج النقر على الإشعار
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      // معالج الرسائل في الخلفية
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

      // التحقق من الرسائل عند فتح التطبيق
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleNotificationTap(initialMessage);
      }

      AppLogger.info('Message handlers setup complete', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.error('Failed to setup message handlers', category: LogCategory.fcm, error: e);
    }
  }

  /// معالج الرسائل في المقدمة
  void _handleForegroundMessage(RemoteMessage message) {
    try {
      AppLogger.info(
        '📨 Foreground message received',
        category: LogCategory.fcm,
        data: {
          'messageId': message.messageId ?? 'unknown',
          'title': message.notification?.title ?? 'No title',
          'body': message.notification?.body ?? 'No body',
        },
      );

      // عرض إشعار محلي
      _showLocalNotification(message);
    } catch (e) {
      AppLogger.error('Error handling foreground message', category: LogCategory.fcm, error: e);
    }
  }

  /// معالج النقر على الإشعار
  void _handleNotificationTap(RemoteMessage message) {
    try {
      AppLogger.info(
        '👆 Notification tapped',
        category: LogCategory.fcm,
        data: {
          'messageId': message.messageId ?? 'unknown',
          'data': message.data,
        },
      );

      // يمكن إضافة منطق التنقل هنا
      _handleNotificationAction(message.data);
    } catch (e) {
      AppLogger.error('Error handling notification tap', category: LogCategory.fcm, error: e);
    }
  }

  /// عرض إشعار محلي
  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      final notification = message.notification;
      if (notification == null) return;

      final androidDetails = AndroidNotificationDetails(
        _channel.id,
        _channel.name,
        channelDescription: _channel.description,
        importance: Importance.high,
        priority: Priority.high,
        playSound: true,
        enableVibration: true,
        enableLights: true,
        icon: '@mipmap/launcher_icon',
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/launcher_icon'),
        styleInformation: BigTextStyleInformation(
          notification.body ?? '',
          contentTitle: notification.title,
          summaryText: 'IIHC',
        ),
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        message.hashCode,
        notification.title,
        notification.body,
        details,
        payload: jsonEncode(message.data),
      );

      AppLogger.info('Local notification shown', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.error('Failed to show local notification', category: LogCategory.fcm, error: e);
    }
  }

  /// معالج النقر على الإشعار المحلي
  void _onNotificationTapped(NotificationResponse response) {
    try {
      AppLogger.info('Local notification tapped', category: LogCategory.fcm);
      
      if (response.payload != null) {
        final data = jsonDecode(response.payload!);
        _handleNotificationAction(data);
      }
    } catch (e) {
      AppLogger.error('Error handling local notification tap', category: LogCategory.fcm, error: e);
    }
  }

  /// معالجة إجراءات الإشعار
  void _handleNotificationAction(Map<String, dynamic> data) {
    try {
      final type = data['type'] as String?;
      
      AppLogger.info(
        'Handling notification action',
        category: LogCategory.fcm,
        data: {'type': type, 'data': data},
      );

      // يمكن إضافة منطق التنقل حسب نوع الإشعار
      switch (type) {
        case 'appointment_reminder':
          // فتح صفحة المواعيد
          break;
        case 'appointment_confirmed':
          // فتح تفاصيل الموعد
          break;
        case 'payment_reminder':
          // فتح صفحة المدفوعات
          break;
        default:
          // إجراء افتراضي
          break;
      }
    } catch (e) {
      AppLogger.error('Error handling notification action', category: LogCategory.fcm, error: e);
    }
  }

  /// الحصول على FCM Token
  Future<String?> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      
      AppLogger.info(
        'FCM Token obtained',
        category: LogCategory.fcm,
        data: {'token': _fcmToken?.substring(0, 20) ?? 'null'},
      );

      // مراقبة تغيير Token
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        AppLogger.info('FCM Token refreshed', category: LogCategory.fcm);
        // يمكن إرسال Token الجديد للخادم هنا
      });

      return _fcmToken;
    } catch (e) {
      AppLogger.error('Failed to get FCM token', category: LogCategory.fcm, error: e);
      return null;
    }
  }

  /// التحقق من إصدار Android
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;
    
    try {
      const platform = MethodChannel('flutter.dev/device_info');
      final int version = await platform.invokeMethod('getAndroidSdkInt');
      return version >= 33; // Android 13 = API level 33
    } catch (e) {
      return false;
    }
  }

  /// الحصول على FCM Token الحالي
  String? get fcmToken => _fcmToken;

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;

  /// اشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      AppLogger.info('Subscribed to topic: $topic', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.error('Failed to subscribe to topic: $topic', category: LogCategory.fcm, error: e);
    }
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      AppLogger.info('Unsubscribed from topic: $topic', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.error('Failed to unsubscribe from topic: $topic', category: LogCategory.fcm, error: e);
    }
  }

  /// تحديث Token في الخادم
  Future<void> updateTokenOnServer() async {
    try {
      if (_fcmToken == null) return;
      
      // هنا يمكن إرسال Token للخادم
      AppLogger.info('Token updated on server', category: LogCategory.fcm);
    } catch (e) {
      AppLogger.error('Failed to update token on server', category: LogCategory.fcm, error: e);
    }
  }
}

/// معالج الرسائل في الخلفية (يجب أن يكون top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  try {
    await Firebase.initializeApp();
    
    AppLogger.info(
      '📨 Background message received: ${message.messageId}',
      category: LogCategory.fcm,
    );
  } catch (e) {
    if (kDebugMode) {
      print('Error handling background message: $e');
    }
  }
}