import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../utils/app_logger.dart';
import 'fcm_service_no_permissions.dart';

/// خدمة تهيئة شاملة للإشعارات في Release (بدون permission_handler)
class NotificationInitializationServiceFixed {
  static final NotificationInitializationServiceFixed _instance = 
      NotificationInitializationServiceFixed._internal();
  factory NotificationInitializationServiceFixed() => _instance;
  NotificationInitializationServiceFixed._internal();

  bool _isInitialized = false;

  /// تهيئة شاملة لنظام الإشعارات
  Future<bool> initializeNotifications() async {
    if (_isInitialized) return true;

    try {
      AppLogger.info('🚀 Starting comprehensive notification initialization');

      // 1. تهيئة Firebase
      await _initializeFirebase();

      // 2. تهيئة FCM Service
      await _initializeFCMService();

      // 3. إعداد معالجات النظام
      await _setupSystemHandlers();

      // 4. تحسينات خاصة بـ Release
      await _applyReleaseOptimizations();

      // 5. اختبار النظام
      await _testNotificationSystem();

      _isInitialized = true;
      AppLogger.info('✅ Notification system initialized successfully');
      
      return true;
    } catch (e) {
      AppLogger.error('❌ Failed to initialize notification system', error: e);
      return false;
    }
  }

  /// تهيئة Firebase
  Future<void> _initializeFirebase() async {
    try {
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
        AppLogger.info('Firebase initialized successfully');
      } else {
        AppLogger.info('Firebase already initialized');
      }
    } catch (e) {
      AppLogger.error('Failed to initialize Firebase', error: e);
      rethrow;
    }
  }

  /// تهيئة FCM Service
  Future<void> _initializeFCMService() async {
    try {
      final fcmService = FCMServiceNoPermissions();
      final success = await fcmService.initialize();
      
      if (!success) {
        throw Exception('FCM Service initialization failed');
      }
      
      AppLogger.info('FCM Service initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize FCM Service', error: e);
      rethrow;
    }
  }

  /// إعداد معالجات النظام
  Future<void> _setupSystemHandlers() async {
    try {
      // معالج أخطاء Flutter
      FlutterError.onError = (FlutterErrorDetails details) {
        AppLogger.error(
          'Flutter Error',
          error: details.exception,
          data: {
            'library': details.library ?? 'unknown',
            'context': details.context?.toString() ?? 'unknown',
          },
        );
      };

      // معالج أخطاء Platform
      PlatformDispatcher.instance.onError = (error, stack) {
        AppLogger.error('Platform Error', error: error);
        return true;
      };

      AppLogger.info('System handlers setup complete');
    } catch (e) {
      AppLogger.warning('Failed to setup system handlers', error: e);
    }
  }

  /// تطبيق تحسينات خاصة بـ Release
  Future<void> _applyReleaseOptimizations() async {
    try {
      if (kReleaseMode) {
        // تحسينات خاصة بـ Release
        await _optimizeForRelease();
      } else {
        // إعدادات Debug
        await _setupDebugMode();
      }
      
      AppLogger.info('Release optimizations applied');
    } catch (e) {
      AppLogger.warning('Failed to apply release optimizations', error: e);
    }
  }

  /// تحسينات Release
  Future<void> _optimizeForRelease() async {
    try {
      // تقليل مستوى التسجيل
      AppLogger.setMinimumLevel(LogLevel.warning);
      
      // تحسينات خاصة بـ Android
      if (Platform.isAndroid) {
        await _optimizeAndroidRelease();
      }
      
      // تحسينات خاصة بـ iOS
      if (Platform.isIOS) {
        await _optimizeIOSRelease();
      }
      
      AppLogger.info('Release optimizations completed');
    } catch (e) {
      AppLogger.warning('Failed to optimize for release', error: e);
    }
  }

  /// تحسينات Android Release
  Future<void> _optimizeAndroidRelease() async {
    try {
      // طلب تجاهل تحسين البطارية
      await _requestBatteryOptimizationExemption();
      
      // تحسين أداء الإشعارات
      await _optimizeAndroidNotifications();
      
      AppLogger.info('Android release optimizations completed');
    } catch (e) {
      AppLogger.warning('Failed to optimize Android release', error: e);
    }
  }

  /// طلب تجاهل تحسين البطارية
  Future<void> _requestBatteryOptimizationExemption() async {
    try {
      const platform = MethodChannel('com.iihcuserone/battery_optimization');
      await platform.invokeMethod('requestIgnoreBatteryOptimizations');
      AppLogger.info('Battery optimization exemption requested');
    } catch (e) {
      AppLogger.warning('Failed to request battery optimization exemption', error: e);
    }
  }

  /// تحسين إشعارات Android
  Future<void> _optimizeAndroidNotifications() async {
    try {
      const platform = MethodChannel('com.iihcuserone/notifications');
      await platform.invokeMethod('optimizeNotifications');
      AppLogger.info('Android notifications optimized');
    } catch (e) {
      AppLogger.warning('Failed to optimize Android notifications', error: e);
    }
  }

  /// تحسينات iOS Release
  Future<void> _optimizeIOSRelease() async {
    try {
      // تحسينات خاصة بـ iOS
      const platform = MethodChannel('com.iihcuserone/ios_notifications');
      await platform.invokeMethod('optimizeNotifications');
      AppLogger.info('iOS release optimizations completed');
    } catch (e) {
      AppLogger.warning('Failed to optimize iOS release', error: e);
    }
  }

  /// إعداد وضع Debug
  Future<void> _setupDebugMode() async {
    try {
      // تفعيل التسجيل المفصل
      AppLogger.setMinimumLevel(LogLevel.debug);
      
      // إعدادات Debug إضافية
      AppLogger.info('Debug mode setup completed');
    } catch (e) {
      AppLogger.warning('Failed to setup debug mode', error: e);
    }
  }

  /// اختبار نظام الإشعارات
  Future<void> _testNotificationSystem() async {
    try {
      final fcmService = FCMServiceNoPermissions();
      
      // التحقق من التهيئة
      if (!fcmService.isInitialized) {
        throw Exception('FCM Service not initialized');
      }
      
      // التحقق من Token
      final token = fcmService.fcmToken;
      if (token == null || token.isEmpty) {
        AppLogger.warning('FCM Token not available yet, but service is initialized');
      }
      
      AppLogger.info(
        'Notification system test passed',
        data: {'tokenLength': token?.length.toString() ?? '0'},
      );
    } catch (e) {
      AppLogger.error('Notification system test failed', error: e);
      rethrow;
    }
  }

  /// التحقق من حالة النظام
  Future<Map<String, dynamic>> getSystemStatus() async {
    try {
      final fcmService = FCMServiceNoPermissions();
      
      return {
        'isInitialized': _isInitialized,
        'fcmInitialized': fcmService.isInitialized,
        'hasToken': fcmService.fcmToken != null,
        'tokenLength': fcmService.fcmToken?.length ?? 0,
        'platform': Platform.operatingSystem,
        'isRelease': kReleaseMode,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('Failed to get system status', error: e);
      return {'error': e.toString()};
    }
  }

  /// إعادة تهيئة النظام
  Future<bool> reinitialize() async {
    try {
      _isInitialized = false;
      return await initializeNotifications();
    } catch (e) {
      AppLogger.error('Failed to reinitialize notification system', error: e);
      return false;
    }
  }

  /// تنظيف الموارد
  Future<void> dispose() async {
    try {
      _isInitialized = false;
      AppLogger.info('Notification system disposed');
    } catch (e) {
      AppLogger.error('Failed to dispose notification system', error: e);
    }
  }

  /// الحصول على حالة التهيئة
  bool get isInitialized => _isInitialized;

  /// فحص أذونات الإشعارات
  Future<bool> checkNotificationPermissions() async {
    try {
      final fcmService = FCMServiceNoPermissions();
      return await fcmService.areNotificationsEnabled();
    } catch (e) {
      AppLogger.error('Failed to check notification permissions', error: e);
      return false;
    }
  }

  /// الحصول على FCM Token
  Future<String?> getFCMToken() async {
    try {
      final fcmService = FCMServiceNoPermissions();
      return fcmService.fcmToken;
    } catch (e) {
      AppLogger.error('Failed to get FCM token', error: e);
      return null;
    }
  }
}