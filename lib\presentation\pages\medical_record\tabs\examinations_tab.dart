import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/utils/app_logger.dart';
import '../../../../data/models/examination_model.dart';

/// تاب الفحوصات - محدث للموديلز الجديدة
class ExaminationsTab extends StatefulWidget {
  final String patientId;

  const ExaminationsTab({super.key, required this.patientId});

  @override
  State<ExaminationsTab> createState() => _ExaminationsTabState();
}

enum ExaminationFilter { all, recent }

class _ExaminationsTabState extends State<ExaminationsTab> {
  final SupabaseClient _supabase = Supabase.instance.client;
  bool _isLoading = true;
  List<ExaminationModel> _examinations = [];
  ExaminationFilter _selectedFilter = ExaminationFilter.all;
  List<String> _availableTypes = [];
  String? _selectedTypeFilter;

  @override
  void initState() {
    super.initState();
    _loadExaminations();
  }

  Future<void> _loadExaminations() async {
    AppLogger.info(
      '🔬 Loading examinations for patient: ${widget.patientId}',
      category: LogCategory.ui,
    );

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _supabase
          .from('examinations')
          .select()
          .eq('patient_id', widget.patientId)
          .order('examination_date', ascending: false);

      final examinations =
          response.map((json) => ExaminationModel.fromJson(json)).toList();

      // استخراج الأنواع المتاحة
      final types = examinations.map((e) => e.type).toSet().toList();
      types.sort();

      AppLogger.info(
        '✅ Loaded ${examinations.length} examinations',
        category: LogCategory.ui,
        data: {
          'count': examinations.length.toString(),
          'types': types.toString(),
        },
      );

      setState(() {
        _examinations = examinations;
        _availableTypes = types;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error(
        '❌ Error loading examinations',
        category: LogCategory.ui,
        error: e,
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        _showErrorSnackBar('فشل في تحميل الفحوصات');
      }
    }
  }

  List<ExaminationModel> get _filteredExaminations {
    List<ExaminationModel> filtered = [];

    // تطبيق فلتر الوقت
    switch (_selectedFilter) {
      case ExaminationFilter.all:
        filtered = _examinations;
        break;
      case ExaminationFilter.recent:
        final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
        filtered =
            _examinations
                .where((exam) => exam.examinationDate.isAfter(thirtyDaysAgo))
                .toList();
        break;
    }

    // تطبيق فلتر النوع
    if (_selectedTypeFilter != null && _selectedTypeFilter!.isNotEmpty) {
      filtered =
          filtered.where((exam) => exam.type == _selectedTypeFilter).toList();
    }

    return filtered;
  }

  String _getFilterTitle(ExaminationFilter filter) {
    switch (filter) {
      case ExaminationFilter.all:
        return 'الكل';
      case ExaminationFilter.recent:
        return 'الحديثة';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    return Column(
      children: [
        _buildFilterTabs(),
        _buildStatsRow(),
        Expanded(child: _buildExaminationsList()),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 40.w,
            height: 40.h,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل الفحوصات...',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterTabs() {
    return Column(
      children: [
        // فلاتر الوقت
        Container(
          margin: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: AppColors.border),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children:
                  ExaminationFilter.values.map((filter) {
                    final isSelected = _selectedFilter == filter;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedFilter = filter;
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 12.h,
                        ),
                        margin: EdgeInsets.all(4.w),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? AppColors.primary
                                  : Colors.transparent,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          _getFilterTitle(filter),
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight:
                                isSelected ? FontWeight.w600 : FontWeight.w400,
                            color:
                                isSelected
                                    ? AppColors.white
                                    : AppColors.textSecondary,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ),

        // فلاتر الأنواع
        if (_availableTypes.isNotEmpty) _buildTypeFilters(),
      ],
    );
  }

  Widget _buildTypeFilters() {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(12.w),
            child: Text(
              'نوع الفحص',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // خيار "الكل"
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedTypeFilter = null;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    margin: EdgeInsets.only(left: 8.w, bottom: 12.h),
                    decoration: BoxDecoration(
                      color:
                          _selectedTypeFilter == null
                              ? AppColors.primary
                              : Colors.transparent,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color:
                            _selectedTypeFilter == null
                                ? AppColors.primary
                                : AppColors.border,
                      ),
                    ),
                    child: Text(
                      'الكل',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight:
                            _selectedTypeFilter == null
                                ? FontWeight.w600
                                : FontWeight.w400,
                        color:
                            _selectedTypeFilter == null
                                ? AppColors.white
                                : AppColors.textSecondary,
                      ),
                    ),
                  ),
                ),
                // أنواع الفحوصات
                ..._availableTypes.map((type) {
                  final isSelected = _selectedTypeFilter == type;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedTypeFilter = type;
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 8.h,
                      ),
                      margin: EdgeInsets.only(left: 8.w, bottom: 12.h),
                      decoration: BoxDecoration(
                        color:
                            isSelected ? AppColors.primary : Colors.transparent,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color:
                              isSelected ? AppColors.primary : AppColors.border,
                        ),
                      ),
                      child: Text(
                        _getExaminationTypeArabic(type),
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w400,
                          color:
                              isSelected
                                  ? AppColors.white
                                  : AppColors.textSecondary,
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getExaminationTypeArabic(String type) {
    switch (type.toLowerCase()) {
      case 'x-ray':
      case 'xray':
      case 'x_ray':
        return 'أشعة سينية';
      case 'blood':
      case 'blood_test':
      case 'blood test':
        return 'تحليل دم';
      case 'ultrasound':
      case 'ultra_sound':
      case 'ultra sound':
        return 'موجات صوتية';
      case 'mri':
      case 'magnetic resonance':
        return 'رنين مغناطيسي';
      case 'ct':
      case 'ct_scan':
      case 'ct scan':
      case 'computed tomography':
        return 'أشعة مقطعية';
      case 'ecg':
      case 'ekg':
      case 'electrocardiogram':
        return 'رسم قلب';
      case 'eeg':
      case 'electroencephalogram':
        return 'رسم مخ';
      case 'hearing_test':
      case 'hearing test':
      case 'audiometry':
        return 'فحص السمع';
      case 'vision_test':
      case 'vision test':
      case 'eye_test':
      case 'eye test':
        return 'فحص النظر';
      case 'urine':
      case 'urine_test':
      case 'urine test':
      case 'urinalysis':
        return 'تحليل بول';
      case 'stool':
      case 'stool_test':
      case 'stool test':
        return 'تحليل براز';
      case 'biopsy':
        return 'خزعة';
      case 'endoscopy':
        return 'منظار';
      case 'colonoscopy':
        return 'منظار القولون';
      case 'mammography':
      case 'mammogram':
        return 'أشعة الثدي';
      case 'bone_density':
      case 'bone density':
      case 'dexa':
      case 'dexa_scan':
        return 'كثافة العظام';
      case 'stress_test':
      case 'stress test':
      case 'exercise_test':
        return 'اختبار الإجهاد';
      case 'allergy_test':
      case 'allergy test':
      case 'skin_test':
        return 'اختبار الحساسية';
      case 'genetic_test':
      case 'genetic test':
      case 'dna_test':
        return 'فحص جيني';
      case 'lab_test':
      case 'lab test':
      case 'laboratory':
        return 'فحص مختبري';
      case 'clinical_exam':
      case 'clinical exam':
      case 'clinical':
        return 'فحص إكلينيكي';
      case 'physical_exam':
      case 'physical exam':
      case 'physical':
        return 'فحص جسدي';
      case 'neurological':
      case 'neuro':
      case 'neurological_exam':
        return 'فحص عصبي';
      case 'cardiac':
      case 'heart':
      case 'cardiac_exam':
        return 'فحص قلبي';
      case 'respiratory':
      case 'lung':
      case 'pulmonary':
        return 'فحص تنفسي';
      case 'dermatological':
      case 'skin':
      case 'dermatology':
        return 'فحص جلدي';
      case 'ophthalmological':
      case 'eye':
      case 'ophthalmology':
        return 'فحص عيون';
      case 'dental':
      case 'teeth':
      case 'oral':
        return 'فحص أسنان';
      case 'orthopedic':
      case 'bone':
      case 'joint':
      case 'musculoskeletal':
        return 'فحص عظام';
      case 'psychiatric':
      case 'mental':
      case 'psychology':
        return 'فحص نفسي';
      case 'gynecological':
      case 'gynecology':
      case 'women':
        return 'فحص نسائي';
      case 'pediatric':
      case 'pediatrics':
      case 'child':
      case 'children':
        return 'فحص أطفال';
      case 'geriatric':
      case 'elderly':
      case 'senior':
        return 'فحص مسنين';
      case 'general':
      case 'general_exam':
      case 'checkup':
      case 'routine':
        return 'فحص عام';
      case 'consultation':
      case 'consult':
        return 'استشارة';
      case 'follow_up':
      case 'follow up':
      case 'followup':
        return 'متابعة';
      case 'assessment':
      case 'evaluation':
        return 'تقييم';
      case 'screening':
        return 'فحص وقائي';
      case 'diagnosis':
      case 'diagnostic':
        return 'تشخيص';
      case 'behavioral':
      case 'behavior':
        return 'فحص سلوكي';
      case 'cognitive':
        return 'فحص معرفي';
      case 'communication':
        return 'فحص التواصل';
      case 'developmental':
        return 'فحص تطويري';
      case 'hearing':
        return 'فحص السمع';
      case 'motor':
        return 'فحص حركي';
      case 'sensory':
        return 'فحص حسي';
      case 'speech':
        return 'فحص النطق';
      case 'other':
        return 'فحص آخر';
      default:
        return type.isNotEmpty ? type : 'نوع غير محدد';
    }
  }

  Widget _buildStatsRow() {
    final filteredCount = _filteredExaminations.length;
    final totalCount = _examinations.length;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.primary, size: 16.sp),
          SizedBox(width: 8.w),
          Text(
            'عرض $filteredCount من أصل $totalCount فحص',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExaminationsList() {
    final filteredExaminations = _filteredExaminations;

    if (filteredExaminations.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadExaminations,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: filteredExaminations.length,
        itemBuilder: (context, index) {
          final examination = filteredExaminations[index];
          return _buildExaminationCard(examination);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    String emptyMessage;
    String emptySubtitle;
    IconData emptyIcon;

    if (_selectedTypeFilter != null && _selectedTypeFilter!.isNotEmpty) {
      emptyMessage = 'لا توجد فحوصات من هذا النوع';
      emptySubtitle = 'لم يتم إجراء فحوصات من النوع المحدد';
      emptyIcon = Icons.search_off;
    } else {
      switch (_selectedFilter) {
        case ExaminationFilter.all:
          emptyMessage = 'لا توجد فحوصات';
          emptySubtitle = 'لم يتم إجراء أي فحوصات بعد';
          emptyIcon = Icons.medical_services;
          break;
        case ExaminationFilter.recent:
          emptyMessage = 'لا توجد فحوصات حديثة';
          emptySubtitle = 'لم يتم إجراء فحوصات خلال الـ 30 يوم الماضية';
          emptyIcon = Icons.schedule;
          break;
      }
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(emptyIcon, size: 64.sp, color: AppColors.textSecondary),
          SizedBox(height: 16.h),
          Text(
            emptyMessage,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            emptySubtitle,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textLight),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildExaminationCard(ExaminationModel examination) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: _getExaminationTypeColor(
              examination.type,
            ).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              _buildCardHeader(examination),
              SizedBox(height: 16.h),

              // معلومات الفحص
              _buildExaminationInfo(examination),

              // الوصف
              if (examination.hasDescription) ...[
                SizedBox(height: 12.h),
                _buildDescriptionSection(examination),
              ],

              // الملاحظات
              if (examination.hasNotes) ...[
                SizedBox(height: 12.h),
                _buildNotesSection(examination),
              ],

              // الصورة
              if (examination.hasImage) ...[
                SizedBox(height: 12.h),
                _buildImageSection(examination),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardHeader(ExaminationModel examination) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: _getExaminationTypeColor(
              examination.type,
            ).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            _getExaminationTypeIcon(examination.type),
            color: _getExaminationTypeColor(examination.type),
            size: 20.sp,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                examination.displayTitle,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                examination.displayTypeArabic,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: _getExaminationTypeColor(examination.type),
                ),
              ),
            ],
          ),
        ),
        _buildDateChip(examination),
      ],
    );
  }

  Widget _buildExaminationInfo(ExaminationModel examination) {
    return Column(
      children: [
        _buildInfoRow(
          Icons.calendar_today,
          'تاريخ الفحص',
          examination.formattedExaminationDate,
        ),
        SizedBox(height: 8.h),
        _buildInfoRow(Icons.schedule, 'منذ', examination.timeFromNow),
        if (examination.createdAt != null) ...[
          SizedBox(height: 8.h),
          _buildInfoRow(
            Icons.add_circle_outline,
            'تاريخ الإضافة',
            examination.formattedCreatedDate,
          ),
        ],
      ],
    );
  }

  Widget _buildDescriptionSection(ExaminationModel examination) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description,
                color: AppColors.textSecondary,
                size: 16.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'وصف الفحص',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            examination.description!,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(ExaminationModel examination) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note, color: AppColors.warning, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                'ملاحظات',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            examination.notes!,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection(ExaminationModel examination) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(Icons.image, color: AppColors.primary, size: 16.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'صورة الفحص متاحة',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          TextButton(
            onPressed: () => _viewImage(examination),
            child: Text(
              'عرض',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateChip(ExaminationModel examination) {
    Color chipColor;
    if (examination.isToday) {
      chipColor = AppColors.success;
    } else if (examination.isPast) {
      chipColor = AppColors.textSecondary;
    } else {
      chipColor = AppColors.primary;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        examination.timeFromNow,
        style: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
          color: chipColor,
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: AppColors.textSecondary, size: 16.sp),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  Color _getExaminationTypeColor(String type) {
    if (type.toLowerCase().contains('x-ray') ||
        type.toLowerCase().contains('أشعة')) {
      return AppColors.primary;
    } else if (type.toLowerCase().contains('blood') ||
        type.toLowerCase().contains('دم')) {
      return AppColors.error;
    } else if (type.toLowerCase().contains('ultrasound') ||
        type.toLowerCase().contains('موجات')) {
      return AppColors.success;
    } else if (type.toLowerCase().contains('mri') ||
        type.toLowerCase().contains('رنين')) {
      return AppColors.warning;
    } else if (type.toLowerCase().contains('ct') ||
        type.toLowerCase().contains('مقطعية')) {
      return Colors.purple;
    } else {
      return AppColors.textSecondary;
    }
  }

  IconData _getExaminationTypeIcon(String type) {
    if (type.toLowerCase().contains('x-ray') ||
        type.toLowerCase().contains('أشعة')) {
      return Icons.medical_services;
    } else if (type.toLowerCase().contains('blood') ||
        type.toLowerCase().contains('دم')) {
      return Icons.bloodtype;
    } else if (type.toLowerCase().contains('ultrasound') ||
        type.toLowerCase().contains('موجات')) {
      return Icons.waves;
    } else if (type.toLowerCase().contains('mri') ||
        type.toLowerCase().contains('رنين')) {
      return Icons.scanner;
    } else if (type.toLowerCase().contains('ct') ||
        type.toLowerCase().contains('مقطعية')) {
      return Icons.camera_alt;
    } else {
      return Icons.medical_information;
    }
  }

  void _viewImage(ExaminationModel examination) {
    if (examination.imageUrl == null || examination.imageUrl!.isEmpty) {
      _showErrorSnackBar('لا توجد صورة متاحة لهذا الفحص');
      return;
    }

    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // شريط العنوان
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.r),
                        topRight: Radius.circular(16.r),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                examination.displayTitle,
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              Text(
                                examination.displayTypeArabic,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: Icon(
                            Icons.close,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // الصورة
                  Flexible(
                    child: Container(
                      padding: EdgeInsets.all(16.w),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12.r),
                        child: Image.network(
                          examination.imageUrl!,
                          fit: BoxFit.contain,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return SizedBox(
                              height: 200.h,
                              child: Center(
                                child: CircularProgressIndicator(
                                  color: AppColors.primary,
                                  value:
                                      loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                          : null,
                                ),
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              height: 200.h,
                              decoration: BoxDecoration(
                                color: AppColors.error.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    size: 48.sp,
                                    color: AppColors.error,
                                  ),
                                  SizedBox(height: 8.h),
                                  Text(
                                    'فشل في تحميل الصورة',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: AppColors.error,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
