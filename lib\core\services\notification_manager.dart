import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'fcm_integration_service.dart';
import 'patient_notification_receiver.dart';
import 'notification_service.dart';
import 'fcm_token_service.dart';

/// مدير نظام الإشعارات الشامل للتطبيق
class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  final FCMIntegrationService _fcmService = FCMIntegrationService();
  final PatientNotificationReceiver _patientReceiver = PatientNotificationReceiver();
  final NotificationService _localNotificationService = NotificationService();
  final FCMTokenService _tokenService = FCMTokenService();
  final SupabaseClient _supabase = Supabase.instance.client;

  bool _isInitialized = false;

  /// تهيئة نظام الإشعارات الكامل
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info(
        '🚀 Initializing complete notification system',
        category: LogCategory.notification,
      );

      // تهيئة الخدمات الأساسية
      await _localNotificationService.initialize();
      await _fcmService.initialize();
      await _patientReceiver.initialize();

      // إعداد معالج الرسائل في الخلفية
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // حفظ FCM Token
      await _saveFCMToken();

      // إعداد مراقبة تحديث التوكن
      _setupTokenRefreshListener();

      _isInitialized = true;

      AppLogger.info(
        '✅ Notification system initialized successfully',
        category: LogCategory.notification,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize notification system',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// حفظ FCM Token في قاعدة البيانات
  Future<void> _saveFCMToken() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        AppLogger.warning(
          '⚠️ No authenticated user to save FCM token for',
          category: LogCategory.notification,
        );
        return;
      }

      final token = await _tokenService.getCurrentToken();
      if (token != null) {
        await _tokenService.saveToken(currentUser.id, token);
        
        AppLogger.info(
          '💾 FCM token saved for user',
          category: LogCategory.notification,
          data: {'userId': currentUser.id},
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save FCM token',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// إعداد مراقبة تحديث التوكن
  void _setupTokenRefreshListener() {
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
      try {
        final currentUser = _supabase.auth.currentUser;
        if (currentUser != null) {
          await _tokenService.saveToken(currentUser.id, newToken);
          
          AppLogger.info(
            '🔄 FCM token refreshed and saved',
            category: LogCategory.notification,
            data: {'userId': currentUser.id},
          );
        }
      } catch (e) {
        AppLogger.error(
          '❌ Failed to handle token refresh',
          category: LogCategory.notification,
          error: e,
        );
      }
    });
  }

  /// معالجة تسجيل دخول المستخدم
  Future<void> onUserLogin(String userId) async {
    try {
      AppLogger.info(
        '👤 Handling user login for notifications',
        category: LogCategory.notification,
        data: {'userId': userId},
      );

      // حفظ التوكن للمستخدم الجديد
      await _saveFCMToken();

      // إعداد الاشتراكات
      await _fcmService.onUserLogin(userId);

      // تهيئة استقبال الإشعارات للمريض
      await _patientReceiver.initialize();

      AppLogger.info(
        '✅ User login handled for notifications',
        category: LogCategory.notification,
        data: {'userId': userId},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user login for notifications',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالجة تسجيل خروج المستخدم
  Future<void> onUserLogout() async {
    try {
      AppLogger.info(
        '👤 Handling user logout for notifications',
        category: LogCategory.notification,
      );

      // إلغاء الاشتراكات
      await _fcmService.onUserLogout();
      await _patientReceiver.unsubscribeFromPatientTopics();

      // حذف التوكن من قاعدة البيانات
      final currentUser = _supabase.auth.currentUser;
      if (currentUser != null) {
        final token = await _tokenService.getCurrentToken();
        if (token != null) {
          await _tokenService.deleteToken(currentUser.id, token);
        }
      }

      // إلغاء جميع الإشعارات المحلية
      await _localNotificationService.cancelAllNotifications();

      AppLogger.info(
        '✅ User logout handled for notifications',
        category: LogCategory.notification,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user logout for notifications',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// طلب أذونات الإشعارات
  Future<bool> requestPermissions() async {
    try {
      // طلب أذونات FCM
      final settings = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      // طلب أذونات الإشعارات المحلية
      final localPermissions = await _localNotificationService.requestPermissions();

      final fcmGranted = settings.authorizationStatus == AuthorizationStatus.authorized;
      final allGranted = fcmGranted && localPermissions;

      AppLogger.info(
        '🔐 Notification permissions requested',
        category: LogCategory.notification,
        data: {
          'fcmGranted': fcmGranted.toString(),
          'localGranted': localPermissions.toString(),
          'allGranted': allGranted.toString(),
        },
      );

      return allGranted;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to request notification permissions',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// عرض إشعار محلي
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      await _localNotificationService.showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: title,
        body: body,
        payload: payload,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to show local notification',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// جدولة إشعار
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    try {
      await _localNotificationService.scheduleNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: title,
        body: body,
        scheduledDate: scheduledDate,
        payload: payload,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to schedule notification',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// جدولة تذكير بالموعد
  Future<void> scheduleAppointmentReminder({
    required String appointmentId,
    required DateTime appointmentDate,
    required String appointmentTime,
    Duration reminderBefore = const Duration(hours: 1),
  }) async {
    try {
      await _localNotificationService.scheduleAppointmentReminder(
        appointmentId: appointmentId,
        patientName: 'أنت',
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        reminderBefore: reminderBefore,
      );

      AppLogger.info(
        '⏰ Appointment reminder scheduled',
        category: LogCategory.notification,
        data: {
          'appointmentId': appointmentId,
          'reminderTime': appointmentDate.subtract(reminderBefore).toIso8601String(),
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to schedule appointment reminder',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// إلغاء تذكير الموعد
  Future<void> cancelAppointmentReminder(String appointmentId) async {
    try {
      await _localNotificationService.cancelNotification(appointmentId.hashCode);
      
      AppLogger.info(
        '🗑️ Appointment reminder cancelled',
        category: LogCategory.notification,
        data: {'appointmentId': appointmentId},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to cancel appointment reminder',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// الحصول على الإشعارات غير المقروءة
  Future<List<Map<String, dynamic>>> getUnreadNotifications() async {
    try {
      return await _patientReceiver.getUnreadNotifications();
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get unread notifications',
        category: LogCategory.notification,
        error: e,
      );
      return [];
    }
  }

  /// تحديد الإشعار كمقروء
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      return await _patientReceiver.markNotificationAsRead(notificationId);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to mark notification as read',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<bool> markAllNotificationsAsRead() async {
    try {
      return await _patientReceiver.markAllNotificationsAsRead();
    } catch (e) {
      AppLogger.error(
        '❌ Failed to mark all notifications as read',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updateNotificationSettings({
    bool enableAppointmentReminders = true,
    bool enableHomeworkNotifications = true,
    bool enableExaminationResults = true,
    bool enableGeneralAnnouncements = true,
  }) async {
    try {
      await _fcmService.updateNotificationSettings(
        enableAppointmentReminders: enableAppointmentReminders,
        enableHomeworkNotifications: enableHomeworkNotifications,
        enableExaminationResults: enableExaminationResults,
      );

      // حفظ الإعدادات محلياً
      await _saveNotificationSettings({
        'appointment_reminders': enableAppointmentReminders,
        'homework_notifications': enableHomeworkNotifications,
        'examination_results': enableExaminationResults,
        'general_announcements': enableGeneralAnnouncements,
      });

      AppLogger.info(
        '⚙️ Notification settings updated',
        category: LogCategory.notification,
        data: {
          'appointments': enableAppointmentReminders.toString(),
          'homework': enableHomeworkNotifications.toString(),
          'examinations': enableExaminationResults.toString(),
          'announcements': enableGeneralAnnouncements.toString(),
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to update notification settings',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// حفظ إعدادات الإشعارات في قاعدة البيانات
  Future<void> _saveNotificationSettings(Map<String, dynamic> settings) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return;

      await _supabase
          .from('user_notification_settings')
          .upsert({
            'user_id': currentUser.id,
            'settings': settings,
            'updated_at': DateTime.now().toIso8601String(),
          });
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save notification settings',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// الحصول على إعدادات الإشعارات
  Future<Map<String, dynamic>> getNotificationSettings() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return _getDefaultSettings();

      final response = await _supabase
          .from('user_notification_settings')
          .select('settings')
          .eq('user_id', currentUser.id)
          .maybeSingle();

      if (response != null && response['settings'] != null) {
        return Map<String, dynamic>.from(response['settings']);
      }

      return _getDefaultSettings();
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get notification settings',
        category: LogCategory.notification,
        error: e,
      );
      return _getDefaultSettings();
    }
  }

  /// الإعدادات الافتراضية للإشعارات
  Map<String, dynamic> _getDefaultSettings() {
    return {
      'appointment_reminders': true,
      'homework_notifications': true,
      'examination_results': true,
      'general_announcements': true,
    };
  }

  /// تنظيف الموارد
  void dispose() {
    AppLogger.info(
      '🧹 Notification manager disposed',
      category: LogCategory.notification,
    );
  }

  /// الحصول على حالة الإشعارات
  Map<String, dynamic> getNotificationStatus() {
    return {
      'isInitialized': _isInitialized,
      'fcmToken': _fcmService.fcmToken,
    };
  }
}

/// معالج الرسائل في الخلفية (مطلوب يكون في المستوى الأعلى)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  AppLogger.info(
    '🔔 Background message received',
    category: LogCategory.notification,
    data: {
      'messageId': message.messageId ?? 'No ID',
      'title': message.notification?.title ?? 'No title',
      'body': message.notification?.body ?? 'No body',
      'data': message.data.toString(),
    },
  );

  // يمكن إضافة منطق إضافي هنا لمعالجة الرسائل في الخلفية
}