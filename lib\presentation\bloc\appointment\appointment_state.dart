part of 'appointment_bloc.dart';

/// حالات المواعيد
abstract class AppointmentState extends Equatable {
  const AppointmentState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class AppointmentInitial extends AppointmentState {}

/// حالة التحميل
class AppointmentLoading extends AppointmentState {}

/// حالة تحميل المواعيد بنجاح
class AppointmentLoaded extends AppointmentState {
  final List<AppointmentModel> appointments;

  const AppointmentLoaded({required this.appointments});

  @override
  List<Object?> get props => [appointments];
}

/// حالة إنشاء موعد بنجاح
class AppointmentCreated extends AppointmentState {
  final AppointmentModel appointment;

  const AppointmentCreated({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

/// حالة تحديث موعد بنجاح
class AppointmentUpdated extends AppointmentState {
  final AppointmentModel appointment;

  const AppointmentUpdated({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

/// حالة إلغاء موعد بنجاح
class AppointmentCancelled extends AppointmentState {
  final AppointmentModel appointment;

  const AppointmentCancelled({required this.appointment});

  @override
  List<Object?> get props => [appointment];
}

/// حالة حذف موعد بنجاح
class AppointmentDeleted extends AppointmentState {}

/// حالة تحميل الأوقات المتاحة
class AvailableTimeSlotsLoaded extends AppointmentState {
  final List<String> timeSlots;

  const AvailableTimeSlotsLoaded({required this.timeSlots});

  @override
  List<Object?> get props => [timeSlots];
}

/// حالة نتائج البحث
class AppointmentSearchResults extends AppointmentState {
  final List<AppointmentModel> appointments;

  const AppointmentSearchResults({required this.appointments});

  @override
  List<Object?> get props => [appointments];
}

/// حالة تحميل الإحصائيات
class AppointmentStatsLoaded extends AppointmentState {
  final Map<String, int> stats;

  const AppointmentStatsLoaded({required this.stats});

  @override
  List<Object?> get props => [stats];
}

/// حالة الخطأ
class AppointmentError extends AppointmentState {
  final Failure failure;

  const AppointmentError({required this.failure});

  @override
  List<Object?> get props => [failure];
}