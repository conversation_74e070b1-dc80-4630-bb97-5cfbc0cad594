import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:googleapis_auth/auth_io.dart';
import '../utils/app_logger.dart';

/// خدمة إرسال الإشعارات باستخدام Google Cloud Service Account
class GoogleFCMService {
  static final GoogleFCMService _instance = GoogleFCMService._internal();
  factory GoogleFCMService() => _instance;
  GoogleFCMService._internal();

  // بيانات Service Account
  static const String _projectId = 'iihc-8841e';
  static const Map<String, dynamic> _serviceAccountJson = ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

  String? _accessToken;
  DateTime? _tokenExpiry;

  /// الحصول على Access Token
  Future<String> _getAccessToken() async {
    try {
      // التحقق من صحة التوكن الحالي
      if (_accessToken != null && 
          _tokenExpiry != null && 
          DateTime.now().isBefore(_tokenExpiry!.subtract(const Duration(minutes: 5)))) {
        return _accessToken!;
      }

      AppLogger.info(
        '🔑 Getting new access token',
        category: LogCategory.notification,
      );

      // إنشاء credentials من Service Account
      final credentials = ServiceAccountCredentials.fromJson(_serviceAccountJson);
      
      // الحصول على Access Token
      final client = await clientViaServiceAccount(
        credentials,
        ['https://www.googleapis.com/auth/firebase.messaging'],
      );

      _accessToken = client.credentials.accessToken.data;
      _tokenExpiry = client.credentials.accessToken.expiry;

      client.close();

      AppLogger.info(
        '✅ Access token obtained successfully',
        category: LogCategory.notification,
        data: {'expiresAt': _tokenExpiry?.toIso8601String()},
      );

      return _accessToken!;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get access token',
        category: LogCategory.notification,
        error: e,
      );
      rethrow;
    }
  }

  /// إرسال إشعار لتوكن واحد
  Future<bool> sendNotificationToToken({
    required String fcmToken,
    required String title,
    required String body,
    Map<String, String>? data,
    String? imageUrl,
  }) async {
    try {
      final accessToken = await _getAccessToken();
      
      final message = {
        'message': {
          'token': fcmToken,
          'notification': {
            'title': title,
            'body': body,
            if (imageUrl != null) 'image': imageUrl,
          },
          if (data != null) 'data': data,
          'android': {
            'priority': 'high',
            'notification': {
              'channel_id': 'iihc_notifications',
              'default_sound': true,
              'default_vibrate_timings': true,
            },
          },
          'apns': {
            'payload': {
              'aps': {
                'alert': {
                  'title': title,
                  'body': body,
                },
                'sound': 'default',
                'badge': 1,
              },
            },
          },
        },
      };

      final response = await http.post(
        Uri.parse('https://fcm.googleapis.com/v1/projects/$_projectId/messages:send'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: json.encode(message),
      );

      if (response.statusCode == 200) {
        AppLogger.info(
          '✅ Notification sent successfully',
          category: LogCategory.notification,
          data: {'token': fcmToken.substring(0, 20) + '...'},
        );
        return true;
      } else {
        AppLogger.error(
          '❌ Failed to send notification',
          category: LogCategory.notification,
          data: {
            'statusCode': response.statusCode.toString(),
            'response': response.body,
          },
        );
        return false;
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error sending notification',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// إرسال إشعار لعدة tokens
  Future<Map<String, bool>> sendNotificationToMultipleTokens({
    required List<String> fcmTokens,
    required String title,
    required String body,
    Map<String, String>? data,
    String? imageUrl,
  }) async {
    final results = <String, bool>{};
    
    AppLogger.info(
      '📤 Sending notification to multiple tokens',
      category: LogCategory.notification,
      data: {'tokenCount': fcmTokens.length.toString()},
    );

    for (final token in fcmTokens) {
      final success = await sendNotificationToToken(
        fcmToken: token,
        title: title,
        body: body,
        data: data,
        imageUrl: imageUrl,
      );
      results[token] = success;
      
      // تأخير بسيط لتجنب rate limiting
      await Future.delayed(const Duration(milliseconds: 100));
    }

    final successCount = results.values.where((success) => success).length;
    
    AppLogger.info(
      '📊 Notification sending completed',
      category: LogCategory.notification,
      data: {
        'total': fcmTokens.length.toString(),
        'successful': successCount.toString(),
        'failed': (fcmTokens.length - successCount).toString(),
      },
    );

    return results;
  }

  /// إرسال إشعار لموضوع (Topic)
  Future<bool> sendNotificationToTopic({
    required String topic,
    required String title,
    required String body,
    Map<String, String>? data,
    String? imageUrl,
  }) async {
    try {
      final accessToken = await _getAccessToken();
      
      final message = {
        'message': {
          'topic': topic,
          'notification': {
            'title': title,
            'body': body,
            if (imageUrl != null) 'image': imageUrl,
          },
          if (data != null) 'data': data,
          'android': {
            'priority': 'high',
            'notification': {
              'channel_id': 'iihc_notifications',
              'default_sound': true,
              'default_vibrate_timings': true,
            },
          },
          'apns': {
            'payload': {
              'aps': {
                'alert': {
                  'title': title,
                  'body': body,
                },
                'sound': 'default',
                'badge': 1,
              },
            },
          },
        },
      };

      final response = await http.post(
        Uri.parse('https://fcm.googleapis.com/v1/projects/$_projectId/messages:send'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: json.encode(message),
      );

      if (response.statusCode == 200) {
        AppLogger.info(
          '✅ Topic notification sent successfully',
          category: LogCategory.notification,
          data: {'topic': topic},
        );
        return true;
      } else {
        AppLogger.error(
          '❌ Failed to send topic notification',
          category: LogCategory.notification,
          data: {
            'topic': topic,
            'statusCode': response.statusCode.toString(),
            'response': response.body,
          },
        );
        return false;
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error sending topic notification',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }
}