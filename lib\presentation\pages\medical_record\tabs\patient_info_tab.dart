import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/utils/app_logger.dart';
import '../../../../data/models/patient_model.dart';

/// تاب معلومات المريض - محدث للموديلز الجديدة
class PatientInfoTab extends StatefulWidget {
  final String patientId;

  const PatientInfoTab({super.key, required this.patientId});

  @override
  State<PatientInfoTab> createState() => _PatientInfoTabState();
}

class _PatientInfoTabState extends State<PatientInfoTab> {
  final SupabaseClient _supabase = Supabase.instance.client;
  bool _isLoading = true;
  PatientModel? _patient;

  @override
  void initState() {
    super.initState();
    _loadPatientInfo();
  }

  Future<void> _loadPatientInfo() async {
    AppLogger.info(
      '👤 Loading patient info: ${widget.patientId}',
      category: LogCategory.ui,
    );

    setState(() {
      _isLoading = true;
    });

    try {
      final response =
          await _supabase
              .from('patients')
              .select()
              .eq('id', widget.patientId)
              .single();

      final patient = PatientModel.fromJson(response);

      AppLogger.info(
        '✅ Loaded patient info',
        category: LogCategory.ui,
        data: {'patientName': patient.name},
      );

      setState(() {
        _patient = patient;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error(
        '❌ Error loading patient info',
        category: LogCategory.ui,
        error: e,
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        _showErrorSnackBar('فشل في تحميل معلومات المريض');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_patient == null) {
      return _buildErrorState();
    }

    return RefreshIndicator(
      onRefresh: _loadPatientInfo,
      color: AppColors.primary,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBasicInfoSection(),
            SizedBox(height: 16.h),
            _buildContactInfoSection(),
            SizedBox(height: 16.h),
            _buildMedicalInfoSection(),
            SizedBox(height: 16.h),
            _buildTreatmentInfoSection(),
            SizedBox(height: 16.h),
            _buildMembershipSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 40.w,
            height: 40.h,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل معلومات المريض...',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: AppColors.error),
          SizedBox(height: 16.h),
          Text(
            'خطأ في تحميل المعلومات',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.error,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'تعذر تحميل معلومات المريض',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadPatientInfo,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return _buildInfoSection(
      title: 'المعلومات الأساسية',
      icon: Icons.person,
      children: [
        _buildInfoRow(Icons.badge, 'الاسم الكامل', _patient!.displayName),
        if (_patient!.hasAge || _patient!.hasBirthDate) ...[
          SizedBox(height: 12.h),
          _buildInfoRow(Icons.cake, 'العمر', _patient!.displayAge),
        ],
        if (_patient!.hasBirthDate) ...[
          SizedBox(height: 12.h),
          _buildInfoRow(
            Icons.calendar_today,
            'تاريخ الميلاد',
            _patient!.formattedBirthDate,
          ),
        ],
        if (_patient!.hasGender) ...[
          SizedBox(height: 12.h),
          _buildInfoRow(
            _patient!.isMale ? Icons.male : Icons.female,
            'الجنس',
            _patient!.displayGender,
          ),
        ],
        if (_patient!.createdAt != null) ...[
          SizedBox(height: 12.h),
          _buildInfoRow(
            Icons.person_add,
            'تاريخ التسجيل',
            _patient!.formattedCreatedDate,
          ),
        ],
      ],
    );
  }

  Widget _buildContactInfoSection() {
    if (!_patient!.hasContactInfo) {
      return const SizedBox.shrink();
    }

    return _buildInfoSection(
      title: 'معلومات التواصل',
      icon: Icons.contact_phone,
      children: [
        if (_patient!.hasPhone) ...[
          _buildInfoRow(Icons.phone, 'رقم الهاتف', _patient!.displayPhone),
        ],
        if (_patient!.hasEmail) ...[
          if (_patient!.hasPhone) SizedBox(height: 12.h),
          _buildInfoRow(
            Icons.email,
            'البريد الإلكتروني',
            _patient!.displayEmail,
          ),
        ],
        SizedBox(height: 12.h),
        _buildInfoRow(
          Icons.contact_mail,
          'وسيلة التواصل الأساسية',
          _patient!.primaryContact,
        ),
      ],
    );
  }

  Widget _buildMedicalInfoSection() {
    if (!_patient!.hasMedicalInfo) {
      return _buildInfoSection(
        title: 'المعلومات الطبية',
        icon: Icons.medical_information,
        children: [_buildEmptyInfo('لا توجد معلومات طبية مسجلة')],
      );
    }

    return _buildInfoSection(
      title: 'المعلومات الطبية',
      icon: Icons.medical_information,
      children: [
        if (_patient!.hasMedicalConditions) ...[
          _buildMedicalInfoCard(
            'الحالات الطبية',
            _patient!.medicalConditions!,
            Icons.local_hospital,
            AppColors.error,
          ),
        ],
        if (_patient!.hasAllergies) ...[
          if (_patient!.hasMedicalConditions) SizedBox(height: 12.h),
          _buildMedicalInfoCard(
            'الحساسية',
            _patient!.allergies!,
            Icons.warning,
            AppColors.warning,
          ),
        ],
        if (_patient!.hasMedications) ...[
          if (_patient!.hasMedicalConditions || _patient!.hasAllergies)
            SizedBox(height: 12.h),
          _buildMedicalInfoCard(
            'الأدوية',
            _patient!.medications!,
            Icons.medication,
            AppColors.primary,
          ),
        ],
        if (_patient!.hasSupplements) ...[
          if (_patient!.hasMedicalConditions ||
              _patient!.hasAllergies ||
              _patient!.hasMedications)
            SizedBox(height: 12.h),
          _buildMedicalInfoCard(
            'المكملات الغذائية',
            _patient!.supplements!,
            Icons.health_and_safety,
            AppColors.success,
          ),
        ],
        if (_patient!.hasPhysicalActivity) ...[
          SizedBox(height: 12.h),
          _buildMedicalInfoCard(
            'النشاط البدني',
            _patient!.physicalActivity!,
            Icons.fitness_center,
            AppColors.primary,
          ),
        ],
        SizedBox(height: 12.h),
        _buildInfoRow(Icons.summarize, 'ملخص طبي', _patient!.medicalSummary),
      ],
    );
  }

  Widget _buildTreatmentInfoSection() {
    return _buildInfoSection(
      title: 'معلومات العلاج',
      icon: Icons.healing,
      children: [
        if (_patient!.hasTreatmentTypes) ...[
          _buildInfoRow(
            Icons.medical_services,
            'أنواع العلاج',
            _patient!.treatmentTypesTextArabic,
          ),
        ] else ...[
          _buildEmptyInfo('لم يتم تحديد أنواع العلاج'),
        ],
        if (_patient!.hasNotes) ...[
          SizedBox(height: 12.h),
          _buildMedicalInfoCard(
            'ملاحظات إضافية',
            _patient!.notes!,
            Icons.note,
            AppColors.textSecondary,
          ),
        ],
      ],
    );
  }

  Widget _buildMembershipSection() {
    return _buildInfoSection(
      title: 'العضوية',
      icon: Icons.card_membership,
      children: [
        _buildInfoRow(
          _patient!.isPremium ? Icons.star : Icons.star_border,
          'نوع العضوية',
          _patient!.membershipStatus,
        ),
      ],
    );
  }

  Widget _buildInfoSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(icon, color: AppColors.primary, size: 20.sp),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // محتوى القسم
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: AppColors.textSecondary, size: 16.sp),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMedicalInfoCard(
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            content,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyInfo(String message) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.textSecondary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.textSecondary, size: 16.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
